# Detailed Services Documentation - Gesture-Driven Presentation System

## 1. AI Processing Services

### 1.1 Vision Language Model (VLM) Integration
- **Location**: `backend/app/services/ai_processor.py`
- **Primary Purpose**: Semantic analysis of presentations
- **Key Features**:
  - Slide content extraction and analysis
  - Key points identification
  - Semantic block detection
  - Content structure parsing
- **Implementation Details**:
  - Uses Phi-3 model (3.8B parameters) via Ollama
  - Offline processing capabilities
  - Batch processing for large presentations
  - Caching of analysis results

### 1.2 Offline VLM Service
- **Location**: `backend/app/services/offline_vlm.py`
- **Purpose**: Local processing of presentation content
- **Features**:
  - Local model inference
  - Text extraction
  - Content summarization
  - Key points generation
- **Benefits**:
  - Reduced latency
  - Privacy preservation
  - No internet dependency
  - Consistent performance

## 2. Voice Processing Services

### 2.1 Voice Processor
- **Location**: `backend/app/services/voice_processor.py`
- **Core Functionality**:
  - Real-time speech recognition
  - Keyword spotting
  - Content tracking
  - Performance feedback
- **Technical Implementation**:
  - Web Speech API integration
  - Buffer management for continuous processing
  - Real-time WebSocket communication
  - Voice session management
- **Features**:
  - Speech-to-text conversion
  - Content verification against key points
  - Real-time feedback generation
  - Session state management

## 3. Storage Services

### 3.1 MongoDB Service
- **Location**: `backend/app/services/mongodb_service.py`
- **Purpose**: Primary data storage
- **Stored Data**:
  - User information
  - Presentation metadata
  - Analysis results
  - Session data
- **Features**:
  - CRUD operations
  - Query optimization
  - Index management
  - Data validation

### 3.2 Redis Manager
- **Location**: `backend/app/services/redis_manager.py`
- **Purpose**: Session and cache management
- **Functionality**:
  - Real-time session tracking
  - Performance caching
  - Temporary data storage
  - WebSocket state management
- **Benefits**:
  - High performance
  - Low latency
  - Scalability
  - Real-time data access

### 3.3 Local Storage Service
- **Location**: `backend/app/services/local_storage.py`
- **Purpose**: File system management
- **Features**:
  - File upload handling
  - Temporary storage
  - Cleanup routines
  - Resource management

## 4. Frontend Services

### 4.1 Gesture Recognition
- **Location**: `frontend/src/components/GestureRecognitionWorking.tsx`
- **Implementation**:
  - MediaPipe integration
  - Hand landmark detection
  - Gesture classification
  - Real-time processing
- **Supported Gestures**:
  - Wave (Next slide)
  - Peace sign (Fullscreen toggle)
  - Open palm (Play/Pause)
  - Pinch (Zoom control)
- **Features**:
  - Real-time feedback
  - Confidence scoring
  - Position tracking
  - Custom gesture mapping

### 4.2 Voice Monitor
- **Location**: `frontend/src/components/VoiceMonitor.tsx`
- **Features**:
  - Speech recognition
  - Content verification
  - Real-time feedback
  - Status indicators
- **Technical Details**:
  - WebSocket integration
  - Audio processing
  - State management
  - Error handling

### 4.3 Presentation Viewer
- **Location**: `frontend/src/components/PresentationViewer.tsx`
- **Features**:
  - Slide rendering
  - Transition effects
  - Gesture control integration
  - Voice command support
- **UI Components**:
  - Presenter view
  - Audience view
  - Control panel
  - Status indicators

## 5. Database Schema

### 5.1 Presentations Collection
```javascript
{
  presentation_id: string,
  title: string,
  status: string,
  total_slides: number,
  upload_timestamp: string,
  user_id: string,
  analysis: {
    total_slides: number,
    key_points: string[],
    semantic_blocks: Array<{
      type: string,
      content: string,
      coordinates: {
        x: number,
        y: number,
        width: number,
        height: number
      }
    }>
  }
}
```

### 5.2 Sessions Collection
```javascript
{
  session_id: string,
  presentation_id: string,
  user_id: string,
  start_time: Date,
  end_time: Date,
  status: string,
  metrics: {
    gesture_accuracy: number,
    content_coverage: number,
    duration: number
  }
}
```

## 6. API Endpoints

### 6.1 Presentation Management
```
POST /api/presentations/upload
GET /api/presentations/{id}
GET /api/presentations/list
DELETE /api/presentations/{id}
```

### 6.2 Session Management
```
POST /api/sessions/start
POST /api/sessions/{id}/end
GET /api/sessions/{id}/status
```

### 6.3 Voice Processing
```
POST /api/voice/process
GET /api/voice/status
```

### 6.4 Authentication
```
POST /api/auth/login
POST /api/auth/register
GET /api/auth/me
```

## 7. WebSocket Events

### 7.1 Gesture Events
```javascript
{
  type: 'gesture',
  data: {
    gesture: string,
    confidence: number,
    position?: {x: number, y: number}
  }
}
```

### 7.2 Voice Events
```javascript
{
  type: 'voice',
  data: {
    text: string,
    confidence: number,
    matches: string[]
  }
}
```

## 8. Future Enhancements

### 8.1 Planned Features
- Advanced analytics dashboard
- Enhanced gesture recognition
- Improved voice command accuracy
- Mobile optimization
- Performance improvements
- Extended error handling

### 8.2 Technical Improvements
- Caching optimization
- Load balancing
- Scalability enhancements
- Security hardening
- Testing automation

## 9. Dependencies

### 9.1 Backend Dependencies
- FastAPI
- MongoDB
- Redis
- Ollama
- MediaPipe
- WebSocket

### 9.2 Frontend Dependencies
- React
- TypeScript
- TailwindCSS
- Framer Motion
- MediaPipe
- Web Speech API

