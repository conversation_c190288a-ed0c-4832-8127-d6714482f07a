\chapter{Introduction}
\label{cha:introduction}

\section{Problem Statement}
\begin{itemize}
    \item This project aims to develop an intelligent, web-based presentation system that replaces traditional clickers with natural hand gestures.
    \item The system will assist presenters by using AI to track slide content and provide real-time feedback.
    \item This approach directly addresses the anxiety and high cognitive load caused by current presentation tools, which force speakers to manage clumsy controls.
\end{itemize}

\section{Motivation}
\begin{itemize}
    \item Our motivation is to reduce presentation-related anxiety and improve communication effectiveness.
    \item We aim to empower speakers by replacing outdated clickers with an intuitive, gesture-based system.
    \item This will help presenters connect more deeply with their content and audience.
    \item The goal is to create a more natural and confident public speaking experience, supported by real-time AI assistance.
\end{itemize}

\section{Objectives}
The core objectives of this project are to design, develop, and evaluate an intelligent presentation system that achieves the following:
\begin{enumerate}
\item \textbf{Develop an AI-powered content pre-processing module:} Utilize a Vision-Language Model (VLM) to automatically analyze presentation slides, deconstruct them into discrete semantic blocks (e.g., titles, bullet points, images), and extract key talking points.
\item \textbf{Implement a real-time, gesture-based control system:} Integrate a high-fidelity, in-browser hand tracking model (MediaPipe Hands) to recognize a vocabulary of natural hand gestures for presentation control, including advancing content, pointing, and zooming.
\item \textbf{Integrate a real-time voice monitoring system:} Employ a lightweight, browser-based keyword spotting engine to listen for the key points extracted by the VLM and provide the presenter with real-time feedback on content coverage.
\item \textbf{Create a unified, low-latency presenter interface:} Build a web-based application that combines the gesture and voice modules, providing the presenter with a private view that includes a dynamic checklist of key points, thereby minimizing the need for memorization.
\item \textbf{Reduce extraneous cognitive load:} Validate through user testing that the integrated system measurably reduces the cognitive burden on presenters compared to traditional control methods.
\end{enumerate}

\section{Novelty of Idea}
The novelty of this project lies not in the invention of any single component, but in the synergistic architecture that integrates three distinct AI technologies into a cohesive, real-time Human-Computer Interaction (HCI) loop. While prior work has explored gesture recognition for post-hoc analysis \cite{zeng2023gesturelens} or isolated control tasks, and VLMs for document parsing \cite{vlm_parsing1, vlm_parsing2}, no existing system combines them in this specific manner. The innovation is the creation of a closed-loop system where:
\begin{itemize}
\item A VLM provides the semantic \textit{structure} of the content beforehand.
\item The presenter's natural \textit{gestures} navigate that structure in real-time.
\item The presenter's \textit{voice} verifies delivery against that structure, providing immediate feedback.
\end{itemize}
This reframes the technologies from classification/analysis tools into components of a continuous, interactive control system, representing a paradigm shift from retrospective analysis to real-time, embodied performance \cite{zeng2023gesturelens}.

\section{Social and Industrial Relevance}
\textbf{Social Relevance:} The project directly addresses glossophobia, a widespread social anxiety that affects a large percentage of the population \cite{glossophobia_stat1, glossophobia_stat2, glossophobia_stat3}. By creating a more natural and less stressful presentation environment, the system has the potential to build confidence, lower communication barriers, and empower individuals who might otherwise avoid public speaking roles. This can have positive impacts in educational settings, community organizing, and personal development.

\textbf{Industrial Relevance:} In corporate and academic environments, effective presentations are crucial for sales, training, knowledge dissemination, and leadership. The proposed system offers a competitive advantage by enabling more dynamic, engaging, and persuasive presentations. By reducing the presenter's cognitive load, it allows them to focus more on audience connection and storytelling, improving communication effectiveness. The web-based, hardware-agnostic nature of the platform makes it an accessible and scalable solution for businesses, universities, and conference organizers seeking to innovate beyond the traditional clicker-and-slide format.