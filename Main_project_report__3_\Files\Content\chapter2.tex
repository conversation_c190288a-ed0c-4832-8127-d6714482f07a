\chapter{Literature Survey}

This chapter presents a comprehensive review of existing research that forms the theoretical and technical foundation for our gesture-driven presentation system. The literature spans three key domains: dynamic hand gesture recognition, lightweight AI model architectures, and presentation analysis systems.

\section{Paper 1 – Detailed review}
\textbf{Title:} \textit{Dynamic Hand Gesture Recognition Based on Short-Term Sampling Neural Networks} \cite{zhang2021dynamic}\\
\textbf{Authors: <AUTHORS>
\textbf{Journal:} IEEE/CAA Journal of Automatica Sinica

\textbf{Summary:} This paper presents a novel deep learning architecture, the Short-Term Sampling Neural Network (STSNN), for dynamic hand gesture recognition from video. The core problem it addresses is the high computational cost and complexity of traditional models like 3D ConvNets while trying to effectively capture both spatial and temporal information. The STSNN methodology involves dividing a video input into a fixed number of frame groups to handle variable video lengths. From each group, it randomly samples an RGB frame and a corresponding optical flow snapshot, which are fused and fed into a shared-parameter Convolutional Neural Network (ConvNet) to learn short-term features. The outputs from the ConvNets are then passed to a Long Short-Term Memory (LSTM) network to model long-range temporal dependencies and produce a final classification.

\textbf{Relevance to Project:} This paper is highly relevant as it provides a robust and computationally efficient model for the core task of dynamic hand gesture recognition. The STSNN architecture, which combines ConvNets for spatial feature extraction and LSTMs for temporal modeling, validates the technical approach of processing video streams for gesture classification. While our project will use MediaPipe for its real-time, in-browser performance, the principles from STSNN regarding the fusion of spatial (hand shape) and temporal (motion) information are directly applicable to our gesture definition and recognition logic.

\section{Paper 2 – Detailed review}
\textbf{Title:} \textit{ALIKE: Accurate and Lightweight Keypoint Detection and Descriptor Extraction} \cite{zhao2023alike}\\
\textbf{Authors: <AUTHORS>
\textbf{Journal:} IEEE Transactions on Multimedia

\textbf{Summary:} This paper introduces ALIKE, an end-to-end deep learning network designed for both accurate and efficient keypoint detection and descriptor extraction. The authors identify a key limitation in many existing methods: the use of non-differentiable Non-Maximum Suppression (NMS) to detect keypoints, which prevents direct optimization of keypoint positions. To solve this, ALIKE proposes a partially differentiable keypoint detection module using softargmax to output sub-pixel accurate keypoints. The model is trained with a novel reprojection loss to directly optimize keypoint positions for repeatability.

\textbf{Relevance to Project:} This paper is conceptually relevant to the AI-powered content analysis phase of our project. While ALIKE detects \textit{visual} keypoints (corners, salient features), our system's VLM detects \textit{semantic} keypoints (titles, bullet points, charts). The underlying principle is analogous: identifying discrete, important elements within a larger visual space. Furthermore, ALIKE's focus on creating a lightweight, real-time network aligns with our project's requirement for a responsive and efficient user experience.
\newpage

\section{Paper 3 – Detailed review}
\textbf{Title:} \textit{Lightweight Speaker Verification Using Transformation Module With Feature Partition and Fusion} \cite{li2024lightweight}\\
\textbf{Authors: <AUTHORS>
\textbf{Journal:} IEEE/ACM Transactions on Audio, Speech, and Language Processing

\textbf{Summary:} This work addresses the challenge of deploying speaker verification systems on low-resource devices by introducing a novel "Transformation Module" (TM). The TM is a plug-and-play component designed to reduce the complexity of existing deep learning models. The module operates by first partitioning a high-dimensional input feature (like an audio spectrogram) into several low-dimensional subsets. To prevent information loss, a fusion step is performed where each subset is updated with inter-subset correlational information. This allows subsequent layers to process smaller inputs, reducing computational load.

\textbf{Relevance to Project:} This paper is directly relevant to the voice recognition module of our system. A key requirement is a real-time, browser-based keyword spotting (KWS) engine with a minimal computational footprint. The principles outlined in this paper—specifically feature partitioning to reduce complexity while using fusion to maintain representational power—provide a strong theoretical and practical basis for designing or selecting such a lightweight audio processing model.

\section{Paper 4 – Detailed review}
\textbf{Title:} \textit{GestureLens: Visual Analysis of Gestures in Presentation Videos} \cite{zeng2023gesturelens}\\
\textbf{Authors: <AUTHORS>
\textbf{Journal:} IEEE Transactions on Visualization and Computer Graphics

\textbf{Summary:} GestureLens is a visual analytics system designed to help public speaking coaches analyze gesture usage in recorded presentation videos. The system addresses the tedious nature of manually reviewing videos by providing a suite of coordinated views to explore the spatial and temporal distribution of a speaker's gestures and their correlation with the spoken transcript. Key features include a heatmap of hand positions, timelines for hand movements, and a linked view connecting gesture clusters to word phrases.

\textbf{Relevance to Project:} This paper is highly relevant as it operates in the exact same domain. It validates the importance of correlating gestures with speech content. However, GestureLens serves as a crucial point of contrast. It is a \textit{retrospective analysis} tool, designed for use after a presentation is complete. Our proposed system is a \textit{real-time control} tool. This distinction is critical: GestureLens answers, "How \textit{were} gestures used?", while our system answers, "How can I \textit{use} my gestures to control the presentation right now?". This paper helps define our project's unique contribution by highlighting the gap between post-hoc analysis and live, embodied interaction.

\section{Paper 5 – Detailed review}
\textbf{Title:} \textit{Deep Learning-Based Approach for Sign Language Gesture Recognition With Efficient Hand Gesture Representation} \cite{alhammadi2020deep}\\
\textbf{Authors: <AUTHORS>
\textbf{Journal:} IEEE Access

\textbf{Summary:} This paper proposes a system for recognizing complex, structured sign language gestures from RGB video. The authors argue that effective sign language recognition requires processing both the local configuration (fine-grained finger shapes) and the global configuration (the hand's position relative to the body). Their methodology uses a two-stream approach. First, an input video is pre-processed into two separate clips: one tightly cropped around the hand region and one showing the broader gesture space. Two separate 3D Convolutional Neural Networks (3DCNNs) extract spatiotemporal features from each stream, which are then fused for final classification.

\textbf{Relevance to Project:} This paper is relevant because it tackles the recognition of gestures that are semantically rich. The paper's core idea of using a two-stream approach to analyze both local and global features is insightful. It parallels our system's need to understand not just the \textit{shape} of a gesture (e.g., "Index Point") but also its \textit{location} within the camera frame to control the virtual spotlight.

\section{Conclusions and Gap Analysis}
The reviewed literature confirms the feasibility and relevance of the core technologies required for this project. Papers by Zhang et al. \cite{zhang2021dynamic} and Al-Hammadi et al. \cite{alhammadi2020deep} demonstrate mature deep learning techniques for robustly recognizing dynamic hand gestures. The work by Zhao et al. \cite{zhao2023alike} provides an analogous framework for identifying salient "keypoints" in visual data, which we extend to the semantic domain. Li et al. \cite{li2024lightweight} offer proven strategies for creating the kind of lightweight, real-time audio processing models our system requires.

However, a significant gap exists. The current research predominantly treats these components in isolation. Gesture recognition systems focus on classification, while tools like GestureLens focus on post-hoc analysis \cite{zeng2023gesturelens}. There is no existing work that synergistically integrates (1) VLM-based semantic content pre-processing, (2) real-time gesture-based control, and (3) real-time voice-based feedback into a single, unified system designed explicitly to reduce the cognitive load of the presenter. Our project directly addresses this gap by shifting the paradigm from analysis or simple classification to a holistic, real-time, embodied control system for presentations.