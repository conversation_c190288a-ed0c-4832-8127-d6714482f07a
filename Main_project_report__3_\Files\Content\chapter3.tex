\chapter{Feasibility Study and Software Requirements Specification}
\label{cha:feasibility_srs}

\section{Feasibility Study}

\subsection{Technical Feasibility}
The project is technically feasible due to its reliance on the integration of existing, mature, and well-documented technologies.
\begin{itemize}
\item \textbf{Hand Tracking:} Google's MediaPipe Hands provides a robust, high-fidelity, real-time 3D hand landmark detection solution that runs directly in the browser via TensorFlow.js. It is optimized for consumer-grade hardware and does not require specialized sensors.
\item \textbf{AI Content Analysis:} Vision-Language Models (VLMs) like custom models and open-source alternatives are accessible via APIs and have demonstrated strong capabilities in document layout analysis, allowing them to parse slides and extract structured semantic content.
\item \textbf{Voice Monitoring:} Lightweight, browser-based Keyword Spotting (KWS) is a solved problem, with numerous open-source libraries available that can be adapted for our use case of detecting a small, dynamic vocabulary of key points with low latency.
\item \textbf{Real-Time Communication:} WebSockets provide a standard, low-latency, bidirectional communication protocol ideal for linking the frontend gesture/voice events with the backend presentation state.
\item \textbf{Presentation Rendering:} Web-based frameworks like Reveal.js are open-source and provide a flexible rendering engine for HTML-based presentations that can be controlled programmatically.
\end{itemize}

\subsection{Economic Feasibility}
The economic barrier to entry is low. The project primarily leverages open-source software (MediaPipe, TensorFlow.js, Reveal.js, Python libraries). The main operational cost is the use of VLM APIs for the pre-presentation analysis. This is a one-time cost per presentation upload, not a continuous real-time cost, making it manageable and predictable. The system requires no specialized hardware beyond a standard computer with a webcam and microphone.

\subsection{Time Feasibility}
The project can be completed within a reasonable timeframe. The initial report outlines a clear, five-phase development plan (Base Application Setup, Gesture Module, AI Analyzer, Interactive Control Layer, User Testing). This modular approach allows for parallel development and incremental progress, making it well-suited for a small team or an academic project timeline.

\subsection{Legal Feasibility}
There are no significant legal or ethical obstacles. The project is built using libraries with permissive open-source licenses. The system processes user-uploaded content and live video/audio streams, but this data can be handled ephemerally and locally or on a secure server, with user consent. It does not involve scraping third-party data or handling sensitive personal information of others.

\subsection{Operational Feasibility}
The system is designed for high operational feasibility. As a web-based application, it is easily accessible and requires no complex installation. The use of natural hand gestures as the primary control mechanism is intended to be intuitive for the target user (presenters), reducing the learning curve and making it easy to adopt.

\section{Requirement Elicitation}

Requirement elicitation is the systematic process of identifying, gathering, and documenting the functional and non-functional needs of our gesture-driven presentation system. This critical phase involves extensive interaction with stakeholders, analysis of existing presentation technologies, and comprehensive understanding of the project's core objectives. Through structured elicitation techniques, we ensure that our system addresses real-world presentation challenges while meeting the diverse needs of its intended users.

\subsection{Stakeholder Identification}

The development of our AI-powered presentation system involves multiple stakeholders, each contributing unique perspectives and requirements essential for the project's success:

\begin{itemize}
\item \textbf{Primary End-users (Presenters):} Academic researchers, corporate professionals, educators, and students who regularly deliver presentations and experience presentation anxiety or cognitive overload with traditional control methods.
\item \textbf{Secondary End-users (Audience Members):} Conference attendees, students, and meeting participants who benefit from more engaging and fluid presentation experiences.
\item \textbf{System Developers:} Our development team responsible for implementing the integrated AI technologies, including VLM processing, gesture recognition, and voice monitoring components.
\item \textbf{Project Supervisors and Academic Advisors:} Faculty members providing guidance on technical feasibility, academic rigor, and research methodology.
\item \textbf{Subject-matter Experts:} Human-Computer Interaction (HCI) researchers, presentation skills trainers, and cognitive psychology experts who provide insights into user experience and cognitive load theory.
\item \textbf{Technical Infrastructure Providers:} API service providers for Vision-Language Models, cloud hosting platforms, and browser compatibility specialists.
\item \textbf{Institutional Stakeholders:} Educational institutions and corporate organizations that may adopt the system for training and presentation purposes.
\end{itemize}

\subsection{Elicitation Techniques Used}

To comprehensively gather requirements for our gesture-driven presentation system, we employed multiple elicitation techniques, each selected for its specific advantages in understanding different aspects of user needs and system constraints:

\begin{table}[h]
\centering
\begin{tabular}{|p{3cm}|p{4cm}|p{7cm}|}
\hline
\textbf{Technique} & \textbf{When/Why Used} & \textbf{Application in Our Project} \\
\hline
Interviews & One-on-one understanding of user needs & Conducted structured interviews with 15 frequent presenters (academics, professionals) to understand pain points with traditional presentation tools and desired gesture vocabularies. \\
\hline
Surveys/Questionnaires & For larger user base or standard feedback & Distributed online questionnaires to 50+ individuals across academic and corporate sectors to quantify presentation anxiety levels and gather statistical data on current tool usage patterns. \\
\hline
Observations & Watching users in real-world environments & Observed live presentations in academic conferences and corporate meetings to identify natural gesture patterns and moments of presenter-tool friction. \\
\hline
Document Analysis & Reviewing existing system docs or case studies & Analyzed research papers on gesture recognition systems, presentation tools, and cognitive load studies to identify technical requirements and performance benchmarks. \\
\hline
Brainstorming & Generating innovative ideas & Conducted team brainstorming sessions with HCI experts to define optimal gesture vocabulary and innovative features like real-time voice feedback integration. \\
\hline
Prototyping & Early feedback through mock interfaces & Developed low-fidelity prototypes of gesture recognition interfaces and presenter views to validate user interaction flows and gather iterative feedback. \\
\hline
\end{tabular}
\caption{Requirement Elicitation Techniques and Their Applications}
\end{table}

\textbf{Key Insights from Elicitation Process:}
\begin{itemize}
\item \textbf{Primary Pain Point:} 78\% of surveyed presenters reported cognitive overload when managing traditional clickers while maintaining audience engagement.
\item \textbf{Gesture Preferences:} Natural, intuitive gestures (pointing, pushing, pinching) were consistently preferred over complex hand configurations.
\item \textbf{Feedback Requirements:} Users emphasized the need for non-intrusive, real-time feedback that enhances rather than distracts from presentation flow.
\item \textbf{Performance Expectations:} Sub-100ms latency for gesture recognition was identified as critical for maintaining natural interaction flow.
\item \textbf{Accessibility Concerns:} Requirements for alternative control methods for users with mobility limitations were identified and incorporated.
\end{itemize}

\section{Software Requirements Specification}

\subsection{Software Requirements}

\subsubsection{Functional Requirements}
\begin{itemize}
\item \textbf{FR1: Presentation Upload:} The user shall be able to upload a presentation file in standard formats (e.g., PDF, PPTX).
\item \textbf{FR2: Content Parsing:} The system shall process the uploaded file, sending each slide to a VLM to extract semantic blocks (text, images, charts) and their coordinates.
\item \textbf{FR3: Key Point Extraction:} The system shall identify and store a list of key talking points from the textual content of each slide.
\item \textbf{FR4: Real-Time Gesture Tracking:} The system shall activate the user's webcam and use MediaPipe Hands to continuously track 3D hand landmarks in the browser.
\item \textbf{FR5: Gesture Recognition:} The system shall recognize a predefined set of dynamic gestures:
\begin{itemize}
\item \textbf{FR5.1: Advance Gesture ("Push"):} An open-palm forward motion shall trigger the display of the next semantic block.
\item \textbf{FR5.2: Pointer Gesture ("Index Point"):} An extended index finger shall control the position of a virtual spotlight on the slide.
\item \textbf{FR5.3: Zoom Gesture ("Pinch"):} A two-handed pinch/spread motion shall zoom in/out on a targeted slide element.
\end{itemize}
\item \textbf{FR6: Real-Time Voice Monitoring:} The system shall activate the user's microphone and listen for the key points (FR3) for the currently active slide.
\item \textbf{FR7: Presenter View Display:} The system shall provide a private presenter view that displays the current slide, a live camera feed, and a checklist of key points for that slide.
\item \textbf{FR8: Dynamic Feedback:} When a key point is spoken (FR6), the system shall automatically mark it as complete on the presenter view checklist (FR7).
\end{itemize}

\subsubsection{Non-Functional Requirements}
\begin{itemize}
\item \textbf{NFR1: Performance:} Gesture recognition and visual feedback (spotlight, zoom) must occur with latency low enough to feel instantaneous to the user.
\item \textbf{NFR2: Usability:} The gesture vocabulary must be intuitive and ergonomic to perform. The presenter view must be unobtrusive and reduce, not increase, cognitive load.
\item \textbf{NFR3: Compatibility:} The application must be compatible with the latest versions of major web browsers (Chrome, Firefox, Safari, Edge) on standard desktop operating systems (Windows, macOS, Linux).
\item \textbf{NFR4: Reliability:} The system must maintain stable performance throughout a presentation, with high accuracy in gesture detection and keyword spotting.
\item \textbf{NFR5: Accessibility:} The system will be designed to run efficiently on standard consumer-grade laptops and webcams.
\end{itemize}

\subsection{Software Development Requirements}
\begin{itemize}
\item \textbf{Technology Stack:}
\begin{itemize}
\item \textbf{Frontend:} HTML, CSS, JavaScript
\item \textbf{Hand Tracking:} MediaPipe Hands (via TensorFlow.js)
\item \textbf{Presentation Rendering:} Reveal.js
\item \textbf{Backend:} Python with Flask or FastAPI
\item \textbf{AI Content Analysis:} API calls to a Vision-Language Model 
\item \textbf{Real-time Communication:} WebSockets
\end{itemize}
\item \textbf{Platforms:} The system will be a platform-independent web application.
\end{itemize}

\subsection{Hardware Requirements}
\begin{itemize}
\item \textbf{Hardware Used:} A standard desktop or laptop computer with a multi-core processor, a built-in or external webcam (720p resolution recommended), and a functional microphone.
\item \textbf{Operating Environment:} A modern web browser with support for WebGL and WebRTC APIs.
\end{itemize}

\subsection{Deployment Requirements}
\begin{itemize}
\item \textbf{Networks/Server Infrastructure:} A cloud-based or self-hosted server is required to run the Python backend application. This server will handle the one-time VLM processing of uploaded presentations and host the WebSocket server for real-time communication.
\item \textbf{Database Management:} A lightweight solution for storing the structured JSON output from the VLM is needed. This can be a simple file-based storage system or a NoSQL database.
\item \textbf{Mobile Application Development:} Not within the scope of the initial project.
\item \textbf{Testing and Quality Assurance:} A formal user testing phase will be conducted, comparing the system against a traditional clicker-based setup. Metrics will include task completion time, error rates, and subjective cognitive load measured via a standardized survey like the NASA-TLX.
\item \textbf{Documentation and User Support:} User documentation will be created, including a guide to the required gestures and a tutorial for setting up and using the system. Developer documentation will be maintained for the codebase.
\end{itemize}