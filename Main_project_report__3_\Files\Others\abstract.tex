\section*{\centering ABSTRACT}

Public speaking anxiety affects up to 77\% of the population, largely due to the extraneous cognitive load imposed by traditional presentation tools that force speakers to simultaneously manage content delivery, audience engagement, and mechanical control interfaces. This project presents a novel real-time, gesture-driven presentation system that integrates three AI technologies to fundamentally reduce this cognitive burden and create a more natural, embodied presentation experience.

The system employs a Vision-Language Model (VLM) to automatically analyze presentation slides and extract key semantic elements and talking points. A real-time hand tracking module using MediaPipe Hands recognizes natural gestures for presentation control, while a lightweight keyword spotting engine monitors the presenter's voice against the extracted key points, providing real-time feedback on content coverage. These components are unified in a web-based application that offers presenters a private view with dynamic content checklists, minimizing memorization requirements.

The innovation lies in creating a closed-loop Human-Computer Interaction system where AI provides semantic structure, natural gestures enable navigation, and voice verification ensures content delivery—transforming isolated technologies into a cohesive, real-time control system. This represents a paradigm shift from retrospective analysis tools to live, embodied performance enhancement.

The system addresses significant social and industrial needs by potentially reducing presentation anxiety, improving communication effectiveness, and providing a scalable, hardware-agnostic solution for educational and corporate environments. By liberating presenters from mechanical control constraints, the system enables more dynamic, engaging, and authentic communication experiences.

\thispagestyle{empty}
\newpage