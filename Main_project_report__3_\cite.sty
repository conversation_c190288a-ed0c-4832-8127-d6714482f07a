%     C I T E . S T Y
%
%     version 3.8  (Apr 1999)
%
%     Compressed, sorted lists of numerical citations: [11-16]
%     see also OVERCITE.STY and DRFTCITE.STY
%
%     Copyright (C) 1989-1999 by <PERSON>
%     These macros may be freely transmitted, reproduced, or modified
%     provided that this notice is left intact.
%
%     Instructions follow \endinput.
%  ------------------------------------
% First, ensure that some catcodes have the expected values
\edef\citenum{% to restore funny codes
  \catcode\string`\string ` \the\catcode\string`\`
  \catcode\string`\string ' \the\catcode\string`\'
  \catcode\string`\string = \the\catcode\string`\=
  \catcode\string`\string _ \the\catcode\string`\_
  \catcode\string`\string : \the\catcode\string`\:}
\catcode\string`\`12
\catcode`\'12
\catcode`\=12
\catcode`\_8
\catcode`\:12

%   Handle optional variations:  [verbose,nospace,space,nosort,noadjust],
%   \citeform,\citeleft,\citeright,\citemid,\citepunct,\citedash
%
%   Set defaults:

%   [ on the left.
\@ifundefined{citeleft}{\let\citeleft=[}{}

%   ] on the right:
\@ifundefined{citeright}{\let\citeright=]}{}

%   , (comma space) before note
\@ifundefined{citemid}{\def\citemid{,\penalty\@medpenalty\ }}{}

%   , (comma thin-space) between entries; [nospace] eliminates the space
\@ifundefined{citepunct}{
   \def\citepunct{,\penalty\@m\hskip.13emplus.1emminus.1em}%
 }{}

%   -- (endash) designating range of numbers:
% (using \hbox avoids easy \exhyphenpenalty breaks)
\@ifundefined{citedash}{\def\citedash{\hbox{--}\penalty\@m }}{}

%   Each number left as-is:
\@ifundefined{citeform}{\def\citeform{}}{}

%   Do not repeat warnings.  [verbose] reverses
\let\oc@verbo\relax

\@ifundefined{DeclareOption}{}%
{ \toks@={\def\oc@verbo#1#2#3#4{}}
  \DeclareOption{verbose}{\the\toks@}
  \DeclareOption{nospace}{\def\citepunct{,\penalty\@m}}
  \DeclareOption{space}{\def\citepunct{,\penalty\@highpenalty\ }}
  \DeclareOption{nosort}{\def\@addto@cite@list
   {\edef\@cite@list{\@cite@list \@celt{\@B@citeB}}}}
  \DeclareOption{sort}{\let\@cite@list\relax}% default!
  \DeclareOption{noadjust}{\let\cite@adjust\@empty}% Don't change spaces
  \DeclareOption{adjust}{\let\cite@adjust\relax}% adjust space before [ ]
  \ProvidesPackage{cite}[1999/04/23 \space  v 3.8]
  \ProcessOptions }

%----------------------
% \citen uses \@nocite to ignore spaces after commas, and write the aux file
% \citation. \citen then loops over the citation tags, using \@make@cite@list
% to make a sorted list of numbers.  Finally, \citen executes \@citelist to
% compress ranges of numbers and print the list. \citen can be used by itself
% to give citation numbers without the brackets and other formatting; e.g.,
% "See also ref.~\citen{junk}."
%
\edef\citen{\noexpand\protect \expandafter\noexpand\csname citen \endcsname}

\@namedef{citen }#1{%
\begingroup
\let\@safe@activesfalse\@empty
\@nocite{#1}% ignores spaces, writes to .aux file, returns #1 in \@no@sparg
\@tempcntb\m@ne    % \@tempcntb tracks highest number
\let\@h@ld\relax   % nothing held from list yet
\let\@citea\@empty % no punctuation preceding first
\let\@celt\delimiter % an unexpandable, but identifiable, token
\def\@cite@list{}% % empty list to start
\@for \@citeb:=\@no@sparg\do{\@make@cite@list}% make a sorted list of numbers
% After sorted citelist is made, execute it to compress citation ranges.
\@tempcnta\m@ne    % no previous number
\let\@celt\@compress@cite \@cite@list % output number list with compression
\@h@ld % output anything held over
\endgroup}

% For each citation, check if it is defined and if it is a number.
% if a number: insert it in the sorted \@cite@list
% otherwise: output it immediately.
%
\def\@make@cite@list{%
 \expandafter\let \expandafter\@B@citeB
          \csname b@\@citeb\@extra@b@citeb \endcsname
 \ifx\@B@citeB\relax % undefined: output ? and warning
    \@citea {\bf{?}}\let\@citea\citepunct \G@refundefinedtrue
    \@warning {Citation `\@citeb' on page \thepage\space undefined}%
    \oc@verbo \global\@namedef{b@\@citeb\@extra@b@citeb}{?}%
 \else %  defined               % remove previous line to repeat warnings
    \ifcat _\ifnum\z@<0\@B@citeB _\else A\fi % a positive number, put in list
       \@addto@cite@list
    \else % citation is not a number, output immediately
       \@citea \citeform{\@B@citeB}%
       \let\@citea\citepunct
 \fi\fi}

% Regular definition for adding entry to cite list, with sorting

\@ifundefined{@addto@cite@list}{% sort option either declared or defaulted
  \def\@addto@cite@list{\@tempcnta\@B@citeB \relax
     \ifnum \@tempcnta>\@tempcntb % new highest, add to end (efficiently)
        \edef\@cite@list{\@cite@list \@celt{\@B@citeB}}%
        \@tempcntb\@tempcnta
     \else % arbitrary number: insert appropriately
        \edef\@cite@list{\expandafter\@sort@celt \@cite@list \@gobble @}%
     \fi}
%
% \@sort@celt inserts number (\@tempcnta) into list of \@celt{num} (#1{#2})
% \@celt must not be expandable; list should end with two vanishing tokens.
%
  \def\@sort@celt#1#2{\ifx \@celt #1% parameters are \@celt {num}
       \ifnum #2<\@tempcnta % number goes later in list
          \@celt{#2}%
          \expandafter\expandafter\expandafter\@sort@celt % continue
       \else % number goes here
          \@celt{\number\@tempcnta}\@celt{#2}% stop comparing
    \fi\fi}
}% end sort option definitions
{}% nosort definition is done in options processing

% Check if each number follows previous and can be put in a range
%
\def\@compress@cite#1{%  % This is executed for each number
  \advance\@tempcnta\@ne % Now \@tempcnta is one more than the previous number
  \ifnum #1=\@tempcnta   % Number follows previous--hold on to it
     \ifx\@h@ld\relax    % first pair of successives
        \edef\@h@ld{\@citea \noexpand\citeform{#1}}%
     \else               % compressible list of successives
        \def\@h@ld{\citedash \citeform{#1}}%
     \fi
  \else   %  non-successor -- dump what's held and do this one
     \@h@ld \@citea \citeform{#1}\let\@h@ld\relax
  \fi \@tempcnta#1\let\@citea\citepunct
}

\edef\cite{\noexpand\protect\expandafter\noexpand\csname cite \endcsname}

\@namedef{cite }{\@ifnextchar [{\@tempswatrue\@citex}{\@tempswafalse\@citex[]}}

%  Make \@citex refer to \citen:
%
\def\@citex[#1]#2{\@cite{\citen{#2}}{#1}}%

%  Replacement for \@cite which defines the formatting normally done
%  around the citation list.  Minor changes can
%  be done by changing \citeleft, \citemid, and \citeright; but in other
%  cases it might be necessary to redefine the whole macro.
%
\def\@cite#1#2{\leavevmode \cite@adjust
  \citeleft{#1\if@tempswa \citemid #2\fi
  \spacefactor\@m % punctuation in note doesn't affect outside
  }\citeright}

%  Put a penalty before the citation, and adjust the spacing: if no space
%  already or if there is extra space due to some punctuation, then change
%  to one inter-word space.
%
\@ifundefined{cite@adjust}{\def\cite@adjust{%
  \@tempskipa\lastskip \edef\@tempa{\the\@tempskipa}\unskip
  \ifnum\lastpenalty=\z@ \penalty\@highpenalty \fi
  \ifx\@tempa\@zero@skip \spacefactor1001 \fi % if no space before, set flag
  \ifnum\spacefactor>\@m \ \else \hskip\@tempskipa \fi}}{}

\edef\@zero@skip{\the\z@skip}

% \nocite: This is changed to ignore *ALL* spaces and be robust.  The
% parameter list, with spaces removed, is `returned' in \@no@sparg, which
% is used by \citen.
%
\edef\nocite{\noexpand\protect\expandafter\noexpand\csname nocite \endcsname}

\@namedef{nocite }#1{\@bsphack \@nocite{#1}%
 \@for \@citeb:=\@no@sparg\do{\@ifundefined{b@\@citeb\@extra@b@citeb}%
    {\G@refundefinedtrue\@warning{Citation `\@citeb' undefined}%
    \oc@verbo \global\@namedef{b@\@citeb\@extra@b@citeb}{?}}{}}%
 \@esphack}

\def\@nocite#1{\xdef\@no@sparg{\@ignsp#1 \! }% remove *ALL* spaces
 \if@filesw \immediate\write\@auxout {\string\citation {\@no@sparg}}\fi}

% for ignoring *ALL* spaces in the input.  This presumes there are no
% \outer tokens and no \if-\fi constructs in the parameter.  Spaces inside
% braces are retained.
%
\def\@ignsp#1 {\ifx\!#1\@empty\else #1\expandafter\@ignsp\fi}

\let\nocitecount\relax  % in case \nocitecount was used for drftcite

\@ifundefined{@extra@b@citeb}{\def\@extra@b@citeb{}}{}
%  in case no fancy bib package (chapterbib) defines this

\@ifundefined{G@refundefinedtrue}{\let\G@refundefinedtrue\relax}{}

\citenum % execute restore-catcodes

% Aliases:
\let\citenum\citen
\let\citeonline\citen

\endinput
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


                      CITE.STY

Modify LaTeX's normal citation mechanism to:

o Sort citation numbers into ascending order; print non-numbers before
  numbers.  Compress lists of three or more consecutive numbers to one
  number range which can be split, with difficulty, after the dash.
  All numbers should be greater than zero.  E.g., if you used to get
  the (nonsense) list [7,5,6,?,4,9,8,Einstein,6], then this style
  will give you [?,Einstein,4-6,6-9].

o Allow, but strongly discourage, line breaks within a series of
  citations.  Each number is separated by a comma and a small space.
  A break at the beginning of an optional note is discouraged also.

o Put a highpenalty before the citation (unless you specifically forbid it
  with ~ ).  Also, adjust the spacing: if there is no space or if there is
  extra space due to some punctuation, then change to one inter-word space.
  E.g.,   Something really stupid\cite{Larry,Curly,Moe}.
  A space is inserted here ------^

o Define \citen to get just the numbers (and write to the aux file)
  without the brackets and extra formatting (`\citen{x}' -> `12').  The
  aliases \citenum and \citeonline are also defined the same as \citen

o `Citation...undefined' warnings are only given once per undefined
  citation tag.  In the text, missing numbers are represented with a
  bold `?' at the first occurrence, and with a normal `?' thenceforth.

o Make \nocite, \cite, and \citen all ignore spaces in the input tags.

Line-breaks are allowed with extra-high penalties (1000) after dashes
and commas; these may have to be taken if TeX sees no other viable
break-points.  If you think citations are being split unnecessarily,
try using \sloppy or a sloppypar environment.

Although each \cite command sorts its numbers, better compression
into ranges can usually be achieved by carefully selecting the order
of the \bibitem entries or the order of initial citations when using
BibTeX.  Having the entries pre-sorted will also save processing time,
especially for long lists of numbers.

Customization:
~~~~~~~~~~~~~~
There are several commands that you may redefine (using \renewcommand)
to change the formatting of citation lists:

 command       function                   default
----------    -----------------------    ----------------------------
\citeform     reformats each number      nothing
\citepunct    printed between numbers    comma + penalty + thin space
\citeleft     left delimiter of list     [
\citeright    right delimiter of list    ]
\citemid      printed before note        comma + space
\citedash     used in a compressed range endash + penalty

Under LaTeX2e, there are four options for \usepackage{cite}:
[verbose] causes warnings for undefined citations to be repeated each
          time they are used.
[nospace] eliminates the spaces after commas in the number list.
[space]   uses a full inter-word space with no penalty after the commas
[nosort]  prevents sorting of the numbers (default is to sort, and a
[sort]    option is provided for completeness).
[noadjust]  disables `smart' handling of space before a cite.

Some examples:
\renewcommand\citemid{; }  % semicolon+space before optional note
\renewcommand\citeleft{(}  % parentheses around list
\renewcommand\citeright{)} % parentheses around list
\renewcommand\citepunct{,} % no space and no breaks at commas
\renewcommand\citeform[1]{\romannumeral 0#1}} % roman numerals [i,vi]
\renewcommand\citeform[1]{(#1)} % parenthesized numbers [(1)-(5)]
\renewcommand\citeform{\thechapter.}  % by chapter: [2.18-2.21]

The appearance of the whole citation list is governed by \@cite, so for
more extensive changes to the formatting, redefine \@cite.  Remember,
these formatting controls are part of this package; they are not part
of generic LaTeX.

Related Note:  cite.sty does not affect the numbering format of the
bibliography; the "[12]" style is still the default.  To change that
format (with or without cite.sty) you can redefine \@biblabel, including

   \renewcommand\@biblabel[1]{#1.}

in your personal style file, or with, for example,

   \makeatletter \renewcommand\@biblabel[1]{(#1)} \makeatother

directly in your document.  If these do not work, your LaTeX and/or
document style are very outdated.

\@extra@b@citeb is a hook for other style files to further specify
citations; for example, to number by chapter (see chapterbib.sty).

See also overcite.sty and drftcite.sty for superscript and draft
mode citations.

% Version 1989: Original.
% Version 1991: Ignore spaces after commas in the parameter list. Move most of
% \citen into \@compress@cite for speed.
% Version 1992: Use \citepunct for commas so it is easier to change.
%
% Version 3.0 (1992):  Rewrite, including sorting.  Make entries like "4th"
% be treated properly as text.
% 3.1: Bug fixes (and Joerg-Martin Schwarz also convinced me to use \ifcat)
% 3.2: Suppress repetitions of warning messages. Include \@extra@b@citeb hook.
% 3.3: Handle LaTeX2e options. Introduce various customization hooks.
% 3.4: Heuristics to avoid removing \hspace glue before the \cite. Make \nocite
%      ignore spaces in list, simplify. Aliases for \citen. Compatability with
%      amsmath (which defines \over).
% 3.6: Include \G@refundefinedtrue and nosort option.
% 3.7: catcode preservation and global \@no@sparg for french.sty; warnings
%      in \nocite.
% 3.8: \citedash hook, noadjust option, hack for babel
%
% Send problem <NAME_EMAIL>

Test file integrity:  ASCII 32-57, 58-126:  !"#$%&'()*+,-./0123456789
:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\]^_`abcdefghijklmnopqrstuvwxyz{|}~
