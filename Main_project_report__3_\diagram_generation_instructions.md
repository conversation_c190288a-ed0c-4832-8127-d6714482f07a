# Instructions for Generating Diagrams

## How to Generate PlantUML Diagrams

### Prerequisites
1. Install PlantUML: https://plantuml.com/download
2. Install Java (required for PlantUML)
3. Optional: Install PlantUML extension for VS Code or your IDE

### Steps to Generate Diagrams

1. **Copy the PlantUML code** from `diagrams_plantuml.md` for each diagram
2. **Save each code block** as separate `.puml` files:
   - `system_architecture.puml`
   - `context_diagram.puml`
   - `level1_dfd.puml`
   - `use_case_diagram.puml`
   - `er_diagram.puml`
   - `gesture_sequence.puml`
   - `content_analysis_sequence.puml`
   - `voice_monitoring_sequence.puml`
   - `multimodal_integration.puml`
   - `system_state_diagram.puml`

3. **Generate PNG images** using PlantUML:
   ```bash
   java -jar plantuml.jar *.puml
   ```
   
   Or if using VS Code with PlantUML extension:
   - Open each `.puml` file
   - Press `Ctrl+Shift+P` and type "PlantUML: Export Current Diagram"
   - Choose PNG format

4. **Rename generated files** to match the LaTeX references:
   - `system_architecture_diagram.png`
   - `context_diagram.png`
   - `level1_dfd.png`
   - `use_case_diagram.png`
   - `er_diagram.png`
   - `gesture_sequence_diagram.png`
   - `content_analysis_sequence.png`
   - `voice_monitoring_sequence.png`
   - `multimodal_integration_activity.png`
   - `system_state_diagram.png`

5. **Place images** in your LaTeX project directory (same level as main.tex or in an images/ folder)

6. **Update LaTeX paths** if needed:
   - If using images/ folder, change `\includegraphics[width=0.9\textwidth]{diagram.png}` 
   - To `\includegraphics[width=0.9\textwidth]{images/diagram.png}`

### Diagram Locations in Report

The diagrams are referenced in the following sections of Chapter 4:

1. **System Architecture Diagram** - Section 4.1.1 (Overall System Architecture)
2. **Context Diagram** - Section 4.1.2 (Level 0 Data Flow Diagram)
3. **Level 1 DFD** - Section 4.1.2 (Level 1 Data Flow Diagram)
4. **Use Case Diagram** - Section 4.1.3 (Primary Use Cases)
5. **ER Diagram** - Section 4.1.5 (Entity-Relationship Diagram)
6. **Gesture Sequence** - Section 4.1.6 (Gesture Recognition Sequence)
7. **Content Analysis Sequence** - Section 4.1.6 (Content Analysis Sequence)
8. **Voice Monitoring Sequence** - Section 4.1.6 (Voice Monitoring Sequence)
9. **Multi-Modal Integration Activity** - Section 4.2.5 (Integration and Optimization)
10. **System State Diagram** - Section 4.3.5 (System State Validation)

### Alternative: Online PlantUML

If you prefer not to install PlantUML locally:
1. Visit: https://www.plantuml.com/plantuml/
2. Paste each PlantUML code block
3. Download the generated PNG
4. Rename and place in your project

### Customization Tips

- **Adjust image sizes** in LaTeX by changing the width parameter:
  - `[width=0.8\textwidth]` for smaller diagrams
  - `[width=1.0\textwidth]` for full-width diagrams
  
- **Change diagram themes** by modifying the first line in PlantUML:
  - `!theme plain` (current)
  - `!theme bluegray`
  - `!theme sketchy-outline`
  
- **Add colors** to specific elements by adding style definitions in PlantUML

### Troubleshooting

- **"Figure not found" error**: Ensure image files are in the correct directory
- **Poor image quality**: Generate SVG instead of PNG for vector graphics
- **Diagram too large**: Adjust `width` parameter or split complex diagrams
- **Compilation errors**: Check that all `\includegraphics` paths are correct