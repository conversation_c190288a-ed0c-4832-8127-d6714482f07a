\documentclass[12pt]{report}
\usepackage[a4paper]{geometry}
\usepackage[myheadings]{fullpage}
\usepackage{import}
\usepackage{fancyhdr}
\usepackage{lastpage}
\usepackage[nopostdot,nonumberlist,acronym,toc,section]{glossaries}
\usepackage{lipsum}  
\usepackage{graphicx, wrapfig, subcaption, setspace, booktabs}
\usepackage[T1]{fontenc}
\usepackage[font=small, labelfont=bf]{caption}
\usepackage{fourier}
\usepackage[protrusion=true, expansion=true]{microtype}
\usepackage[english]{babel}
\usepackage{sectsty}
\usepackage{url, lipsum}
\usepackage[utf8]{inputenc}
\usepackage{appendix}
\usepackage{multicol}
\usepackage{graphicx}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{array}
\usepackage{cite}
\usepackage{setspace}
%\usepackage{natbib}
\usepackage{amsmath}
\usepackage{amssymb}
\renewcommand{\baselinestretch}{1.5}
\usepackage[export]{adjustbox}
\usepackage[nottoc]{tocbibind}
\usepackage{setspace}
% Load the package

\renewcommand{\glossarysection}[2][]{}

 %Define all the acronyms in acronyms.tex
\import{Files/Others/}{acronyms.tex}

%Define all the nomenclatures in notation.tex
\import{Files/Others/}{nomenclature.tex}

% Generate the glossary
\makeglossaries

\setcounter{tocdepth}{5}
\setcounter{secnumdepth}{5}
\pagestyle{plain}


% For date
\renewcommand{\today}{\ifcase \month \or January\or February\or March\or %
April\or May \or June\or July\or August\or September\or October\or November\or %
December\fi, \number \year} 

%make TOC clickable
\usepackage{hyperref}
\hypersetup{
    colorlinks,
    citecolor=black,
    filecolor=black,
    linkcolor=black,
    urlcolor=black
}
\addto{\captionsenglish}{\renewcommand{\bibname}{References}}

\begin{document}


\pagenumbering{gobble}
\import{Files/Others/}{front_page.tex}

\import{Files/Others/}{declaration.tex}

\import{Files/Others/}{certificate.tex}

\pagenumbering{roman}

\import{Files/Others/}{acknowledgement.tex}

\import{Files/Others/}{abstract.tex}

\tableofcontents
\newpage

\listoffigures

\listoftables
\newpage

%List all abreviations and their expansions
\import{Files/Others/}{abbreviations.tex}

%List all symbols used in the report,give units in case of dimensional quantities
\import{Files/Others/}{notation.tex}

%-------------------------------------------------------------------------------
% Section title formatting
\sectionfont{\scshape}
%-------------------------------------------------------------------------------

%-------------------------------------------------------------------------------
% BODY
%-------------------------------------------------------------------------------
\clearpage

% \pagestyle{fancy}
\fancyhf{}
\setlength\headheight{15pt}
\fancyhead[L]{}
\fancyhead[R]{\chaptermark}
\cfoot{\thepage}

\pagenumbering{arabic}

\import{Files/Content/}{chapter1.tex}

\import{Files/Content/}{chapter2.tex}

\import{Files/Content/}{chapter3.tex}

\import{Files/Content/}{chapter4.tex}

\import{Files/Content/}{chapter5.tex}

\import{Files/Content/}{chapter6.tex}

\import{Files/Others/}{references.tex}

\end{document}

