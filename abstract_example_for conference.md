SmartBoardX: Real-Time Class Note Digitization System
<PERSON><PERSON><PERSON>1
, <PERSON><PERSON>
, <PERSON><PERSON><PERSON><PERSON>
, <PERSON><PERSON><PERSON><PERSON>4
, Prof. <PERSON><PERSON><PERSON>
, Dr<PERSON>6
1 2 3 4 5 6Department of Computer Science and Engineering, Government Engineering College, Kozhikode, Kerala,
673005, India
<EMAIL>
ABSTRACT
In the traditional classroom, handwritten notes on the board are usually rubbed off after a lecture, giving
students no chance to correct or make up for missing a session. This issue is even more pressing for students
with impairments like visual, learning, or motor disabilities, who are likely to find it difficult to read from
the board, keep up with note taking, or be an active participant in the learning process. Other than students,
instructors are also affected - precious class time is often wasted as students concentrate on note taking
instead of following the lesson, which can result in incomplete lessons on chapters and less teaching
efficiency.
SmartBoardX, a smart classroom aid, has been developed to capture and digitize handwritten board
content in real time. The information is automatically projected onto a student's phone, enabling students -
sitting far away from the board, taking classes remotely, or having extra needs - to keep up with lessons
effortlessly without lagging behind in note taking. It combines both software and hardware elements to
deliver its functionality. Hardware installation consists of a Raspberry Pi with an attached camera module
that reads handwritten board content on an ongoing basis. Frames taken are processed through
Convolutional Neural Network (CNN) methods trained on handwritten data to identify and digitize text.
Processed results are formatted and sent to the Firebase storage. On the software side, flutter application
offers real-time access, session control and shared note editing support - making interaction fluid and
accessible for teachers as well as students. The notes are stored and formatted into neatly organized digital
documents for easy access at all times for revision and study.
By combining affordable hardware, live note capture, cross-platform software, and cloud integration,
SmartBoardX unites old-style teaching with new-age digital access. It is not merely a tech gadget, but a
socially necessary move toward inclusive learning and effective teaching - enabling the student to learn
better while freeing the educator to teach entire, immersive lessons.
Keywords: Real-time note capture; Handwritten board content; Digital classroom; Teaching efficiency;
Remote accessibility.