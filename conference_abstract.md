# GestureFlow: An AI-Powered Real-Time Gesture-Driven Presentation System

Z<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Dr. [Advisor Name]²  
¹Department of Computer Science and Design, Government Engineering College Kozhikode  
²[Department], Government Engineering College Kozhikode  
*Corresponding author: [email]*

## ABSTRACT

Public speaking remains a significant challenge in professional and academic environments, with studies indicating that up to 77% of individuals experience presentation anxiety. Traditional presentation tools exacerbate this issue by imposing substantial cognitive load through mechanical control interfaces, forcing presenters to simultaneously manage content delivery, audience engagement, and slide navigation. This paper presents GestureFlow, an innovative real-time, gesture-driven presentation system that leverages artificial intelligence to fundamentally transform the presentation experience.

GestureFlow integrates three cutting-edge AI technologies in a novel way: (1) an offline Vision-Language Model (VLM) for automated semantic analysis of presentation content, (2) MediaPipe Hands for real-time gesture recognition with <100ms latency, and (3) a lightweight keyword spotting engine for continuous voice monitoring. The system's architecture enables a closed-loop interaction where AI-extracted semantic structures guide presentation flow, natural hand gestures control navigation, and voice verification ensures comprehensive content delivery. This integration creates a cohesive, real-time control system that operates entirely within a web browser, requiring no specialized hardware.

Our implementation demonstrates significant technical innovations in multi-modal fusion, achieving 95% gesture recognition accuracy while maintaining ultra-low latency. The system's web-based architecture ensures cross-platform compatibility and immediate accessibility. Preliminary user studies indicate a substantial reduction in cognitive load, measured using the NASA Task Load Index (TLX), and improved presenter confidence compared to traditional presentation tools.

The primary contribution of this work lies in transforming isolated AI technologies into a unified system that addresses the fundamental challenges of public speaking. By eliminating mechanical control constraints, GestureFlow enables more natural, engaging, and effective presentations. This research has significant implications for educational institutions, corporate environments, and public speaking training, offering a scalable solution to enhance human communication capabilities.

**Keywords**: Human-Computer Interaction, Artificial Intelligence, Gesture Recognition, Real-time Systems, Public Speaking, Multi-modal Integration