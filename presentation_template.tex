\documentclass{beamer}

\usepackage{url}
\usetheme{CambridgeUS}
\usecolortheme{dolphin}
\setbeamercolor{footline}{fg=white, bg=black}
\usepackage[backend=bibtex, sorting=none, maxbibnames=5]{biblatex}
\renewcommand*{\bibfont}{\tiny}
\setbeamertemplate{bibliography item}{\insertbiblabel}
\renewcommand{\footnotesize}{\tiny}
\usefonttheme{professionalfonts}
\bibliography{seminar}
\useinnertheme{rectangles}
\usepackage{pgfgantt}
\usepackage{adjustbox}

%%% change default style of beamer bibliography %%%
\setbeamertemplate{bibliography item}{\insertbiblabel}

%%%%%%%%   remove bottom navigation bar   %%%%%%%%%
\beamertemplatenavigationsymbolsempty
\usepackage{ragged2e}       %justification
\usepackage{graphicx} 	% Allows including images
\usepackage{fixltx2e} 	% need to use the \textsubscript{} command
\usepackage{algorithmic} 	% need to write some algorithms
\usepackage{amsmath}
\usepackage{hyperref}

% need to write some equations

\title[AI Presenter]{\textbf{AI-Powered Gesture-Driven Presentation System}}
% \titlegraphic{\includegraphics[width=2cm,height=1.5cm]{logo.jpg}} % Add your logo here
\author[Ziyad Ahammed] % optional short version for footline
{
\large
\vspace{3mm}
\\
Ziyad Ahammed \\
% Add other team members here if any \\
\vspace{3mm}
% S\textsubscript{7} B.Tech CSD \ % Uncomment and edit if needed
\vspace{3mm}
\emph{Guided by:} [Your Guide\'s Name] \[12pt]
\makebox[\textwidth]{\includegraphics[width=2cm,height=1.5cm]{logo.jpg}} \[6pt] % Add your logo here
Department of Computer Science and Engineering \ 
% Your College/University Name \ 
}
\date[]{\today}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Uncomment this to include a table of contents before every section %%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%\AtBeginSection[]
%{ 
% 	 \begin{frame}[plain]{Outline}
% 	 \tableofcontents[currentsection]
% 	 \end{frame}
%}

\begin{document}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\begin{frame}[plain]
  \titlepage
\end{frame}

\begin{frame}
\frametitle{\textbf{Outline:}}
\tableofcontents
\end{frame}

%%%%%%%%%%%% Introduction %%%%%%%%%%%% 
\section{SYSTEM ARCHITECTURE & TECHNOLOGIES}
\begin{frame}[c]
\frametitle{System Architecture}
\justifying
\begin{itemize}
    \item \textbf{Frontend:} Developed with React, TypeScript, and Vite for a fast, modern UI.
    \item \textbf{Backend:} Built with Python and FastAPI, providing a high-performance API.
    \item \textbf{Real-time Communication:} Uses WebSockets for instant gesture and voice command processing.
    \item \textbf{AI/ML:} Integrates PyTorch, Transformers, and OpenCV for advanced document analysis.
    \item \textbf{Database:} Utilizes MongoDB for storing presentation data and Redis for caching and session management.
    \item \textbf{Containerization:} Fully containerized with Docker and Docker Compose for easy setup and deployment.
\end{itemize}
\end{frame}

\section{FEATURES IMPLEMENTED}
\begin{frame}[c]
\frametitle{Features Implemented}
\justifying
\begin{itemize}
    \item \textbf{Gesture Recognition:} Real-time hand gesture detection in the browser using MediaPipe for presentation control (next/previous slide, zoom, etc.).
    \item \textbf{Voice-to-Text:} In-browser speech-to-text using the Web Speech API to capture the presenter\'s speech.
    \item \textbf{AI-Powered Keyword Spotting:} A backend service that uses fuzzy matching to detect when the presenter covers key topics.
    \item \textbf{Document Analysis:} Uploaded presentations (.pdf, .pptx) are automatically analyzed to extract key points, themes, and structure.
    \item \textbf{Real-time Feedback:} Live updates to the UI to show detected gestures and covered key points.
    \item \textbf{Full System Containerization:} The entire application stack can be launched with a single `docker-compose up` command.
\end{itemize}
\end{frame}

\section{LINKS}
\begin{frame}[c]
\frametitle{Links}
\justifying
\begin{itemize}
  \item \textbf{GitHub Link:} \url{[Your GitHub Project URL]}
  \item \textbf{Hosted Link:} \url{[Your Application\'s Hosted URL]}
\end{itemize}
\end{frame}

% ---------------- Slide 1 ----------------

\begin{frame}
\frametitle{Frontend Demo}
\begin{figure}[H]
    \centering
    % Replace 'homepage.png' with the path to your screenshot
    \includegraphics[width=\textwidth]{homepage.png} 
    \caption{Presenter View with Gesture and Voice Feedback}
    \label{fig:system-architecture}
\end{figure}
\end{frame}

\section{REMAINING WORK / FUTURE SCOPE}
\begin{frame}[c]
    \frametitle{Remaining Work / Future Scope}
    \begin{itemize}
        \item Enhance the accuracy of gesture and voice recognition models.
        \item Add more complex presentation control features (e.g., jumping to a specific slide).
        \item Implement user accounts and authentication to save presentation history.
        \item Conduct comprehensive user testing to gather feedback and improve usability.
        \item Deploy the application to a cloud platform for public access.
    \end{itemize}
\end{frame}

\section{THE END}
	\begin{frame}
    \centering
    \Huge{\textbf{Thank You}}
    \vfill
    \Large{Questions?}
	% Or use an image: \includegraphics[scale=.7]{thnk.PNG}
	\end{frame}
	
\end{document}
