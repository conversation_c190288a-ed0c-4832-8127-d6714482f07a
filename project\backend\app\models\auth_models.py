"""
Authentication Models and Schemas for AI-Powered Presentation System
"""

from pydantic import BaseModel, EmailStr, Field
from typing import Optional, Dict, Any
from datetime import datetime

# Request Models
class UserRegister(BaseModel):
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=6, max_length=100)
    full_name: Optional[str] = Field(None, max_length=100)

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"

class TokenData(BaseModel):
    email: Optional[str] = None

# Response Models
class UserResponse(BaseModel):
    id: str = Field(alias="_id")
    email: str
    username: str
    full_name: str
    created_at: datetime
    is_active: bool
    presentation_count: int = 0

    class Config:
        populate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class AuthResponse(BaseModel):
    success: bool
    message: str
    user: Optional[UserResponse] = None
    token: Optional[Token] = None

class UserProfile(BaseModel):
    id: str = Field(alias="_id")
    email: str
    username: str
    full_name: str
    created_at: datetime
    presentation_count: int = 0
    
    class Config:
        populate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Database Models (for internal use)
class UserInDB(BaseModel):
    id: Optional[str] = Field(None, alias="_id")
    email: str
    username: str
    full_name: str
    hashed_password: str
    created_at: datetime
    is_active: bool = True
    presentation_count: int = 0

    class Config:
        populate_by_name = True
