from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum
import uuid

class GestureType(str, Enum):
    PUSH = "push"
    POINT = "point"
    PINCH = "pinch"
    UNKNOWN = "unknown"

class PresentationStatus(str, Enum):
    UPLOADING = "uploading"
    PROCESSING = "processing"
    READY = "ready"
    ERROR = "error"

class SessionStatus(str, Enum):
    CREATED = "created"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"

class BlockType(str, Enum):
    TITLE = "title"
    TEXT = "text"
    IMAGE = "image"
    CHART = "chart"
    BULLET_POINT = "bullet_point"

class Priority(str, Enum):
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

# Core Models
class Coordinates(BaseModel):
    x: int
    y: int
    width: int
    height: int

class SemanticBlock(BaseModel):
    block_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    type: BlockType
    content: str
    coordinates: Coordinates
    importance_score: float = Field(ge=0.0, le=1.0)

class KeyPoint(BaseModel):
    point_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    text: str
    keywords: List[str]
    priority: Priority
    spoken_status: bool = False

class Slide(BaseModel):
    slide_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    slide_number: int
    image_url: str
    semantic_blocks: List[SemanticBlock] = []
    key_points: List[KeyPoint] = []
    estimated_duration: int = 60  # seconds
    
class Presentation(BaseModel):
    presentation_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str
    file_path: str
    upload_timestamp: datetime = Field(default_factory=datetime.utcnow)
    processing_status: PresentationStatus = PresentationStatus.UPLOADING
    slides: List[Slide] = []
    total_slides: int = 0
    
class GestureEvent(BaseModel):
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    session_id: str
    gesture_type: GestureType
    confidence_score: float = Field(ge=0.0, le=1.0)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    coordinates: Optional[Dict[str, Any]] = None

class VoiceEvent(BaseModel):
    voice_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    session_id: str
    detected_keyword: str
    confidence_score: float = Field(ge=0.0, le=1.0)
    audio_duration: float
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class SessionState(BaseModel):
    active_slide: int = 0
    visible_blocks: List[str] = []
    spotlight_position: Optional[Dict[str, float]] = None
    zoom_level: float = 1.0
    last_gesture: Optional[str] = None
    last_action_timestamp: datetime = Field(default_factory=datetime.utcnow)

class Session(BaseModel):
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    presentation_id: str
    status: SessionStatus = SessionStatus.CREATED
    start_time: datetime = Field(default_factory=datetime.utcnow)
    end_time: Optional[datetime] = None
    current_state: SessionState = Field(default_factory=SessionState)
    gesture_events: List[GestureEvent] = []
    voice_events: List[VoiceEvent] = []

# Request/Response Models
class PresentationUploadRequest(BaseModel):
    title: str
    
class PresentationUploadResponse(BaseModel):
    presentation_id: str
    status: str
    message: str

class SessionCreateRequest(BaseModel):
    presentation_id: str
    
class SessionCreateResponse(BaseModel):
    session_id: str
    status: str
    message: str

class GestureDetectionRequest(BaseModel):
    session_id: str
    gesture_type: GestureType
    confidence_score: float
    coordinates: Optional[Dict[str, Any]] = None
    
class VoiceDetectionRequest(BaseModel):
    session_id: str
    detected_keyword: str
    confidence_score: float
    audio_duration: float

class PresentationStateUpdate(BaseModel):
    session_id: str
    active_slide: Optional[int] = None
    visible_blocks: Optional[List[str]] = None
    spotlight_position: Optional[Dict[str, float]] = None
    zoom_level: Optional[float] = None

# Response Models
class ApiResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    
class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    details: Optional[str] = None