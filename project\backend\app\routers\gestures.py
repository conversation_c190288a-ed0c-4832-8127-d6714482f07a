from fastapi import APIRouter, HTTPException
from datetime import datetime

from app.models.schemas import (
    GestureDetectionRequest,
    GestureEvent,
    ApiResponse
)
from app.services.database import get_database, insert_document
from app.services.redis_manager import get_redis

router = APIRouter()

@router.post("/detect")
async def process_gesture_detection(request: GestureDetectionRequest):
    """Process a detected gesture and update presentation state"""
    
    # Verify session exists and is active
    redis_manager = await get_redis()
    session_data = await redis_manager.get_session(request.session_id)
    
    if not session_data:
        raise HTTPException(status_code=404, detail="Session not found")
    
    if session_data["status"] != "active":
        raise HTTPException(status_code=400, detail="Session is not active")
    
    try:
        # Create gesture event
        gesture_event = GestureEvent(
            session_id=request.session_id,
            gesture_type=request.gesture_type,
            confidence_score=request.confidence_score,
            coordinates=request.coordinates
        )
        
        # Store in database
        db = await get_database()
        await insert_document("gesture_events", gesture_event.dict())
        
        # Process gesture command based on type
        command_result = await process_gesture_command(gesture_event, session_data)
        
        # Update session state if needed
        if command_result.get("state_update"):
            await redis_manager.update_session(
                request.session_id, 
                {"current_state": command_result["state_update"]}
            )
        
        return ApiResponse(
            success=True,
            message=f"Gesture {request.gesture_type} processed successfully",
            data={
                "gesture_event_id": gesture_event.event_id,
                "command_executed": command_result.get("command"),
                "state_update": command_result.get("state_update")
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Gesture processing failed: {str(e)}")

async def process_gesture_command(gesture_event: GestureEvent, session_data: dict):
    """Process gesture command and determine state updates"""
    
    current_state = session_data.get("current_state", {})
    result = {"command": None, "state_update": None}
    
    if gesture_event.gesture_type == "push":
        # Advance to next content block or slide
        active_slide = current_state.get("active_slide", 0)
        visible_blocks = current_state.get("visible_blocks", [])
        
        # Logic for advancing content
        result["command"] = "advance_content"
        result["state_update"] = {
            **current_state,
            "last_gesture": "push",
            "last_action_timestamp": datetime.utcnow().isoformat()
        }
        
    elif gesture_event.gesture_type == "point":
        # Update spotlight position
        if gesture_event.coordinates:
            result["command"] = "update_spotlight"
            result["state_update"] = {
                **current_state,
                "spotlight_position": gesture_event.coordinates,
                "last_gesture": "point",
                "last_action_timestamp": datetime.utcnow().isoformat()
            }
    
    elif gesture_event.gesture_type == "pinch":
        # Zoom in/out
        current_zoom = current_state.get("zoom_level", 1.0)
        zoom_change = gesture_event.coordinates.get("zoom_delta", 0) if gesture_event.coordinates else 0
        new_zoom = max(0.5, min(3.0, current_zoom + zoom_change))
        
        result["command"] = "zoom"
        result["state_update"] = {
            **current_state,
            "zoom_level": new_zoom,
            "last_gesture": "pinch",
            "last_action_timestamp": datetime.utcnow().isoformat()
        }
    
    return result

@router.get("/{session_id}/history")
async def get_gesture_history(session_id: str, limit: int = 50):
    """Get gesture event history for a session"""
    
    db = await get_database()
    events = await db.gesture_events.find(
        {"session_id": session_id}
    ).sort("timestamp", -1).limit(limit).to_list(length=None)
    
    # Remove MongoDB _id fields
    for event in events:
        event.pop("_id", None)
    
    return ApiResponse(
        success=True,
        message=f"Retrieved {len(events)} gesture events",
        data={"events": events}
    )

@router.get("/{session_id}/stats")
async def get_gesture_statistics(session_id: str):
    """Get gesture usage statistics for a session"""
    
    db = await get_database()
    
    # Aggregate gesture statistics
    pipeline = [
        {"$match": {"session_id": session_id}},
        {"$group": {
            "_id": "$gesture_type",
            "count": {"$sum": 1},
            "avg_confidence": {"$avg": "$confidence_score"},
            "last_used": {"$max": "$timestamp"}
        }}
    ]
    
    stats = await db.gesture_events.aggregate(pipeline).to_list(length=None)
    
    return ApiResponse(
        success=True,
        message="Gesture statistics retrieved successfully",
        data={"statistics": stats}
    )