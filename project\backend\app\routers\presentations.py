from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import JSONResponse
import os
import uuid
import asyncio
from datetime import datetime
from typing import List

from app.models.schemas import (
    PresentationUploadRequest, 
    PresentationUploadResponse,
    Presentation,
    PresentationStatus,
    ApiResponse
)
from app.services.local_storage import get_storage
from app.services.offline_vlm import process_presentation_async
from app.utils.config import get_settings

router = APIRouter()
settings = get_settings()

@router.post("/upload", response_model=PresentationUploadResponse)
async def upload_presentation(
    file: UploadFile = File(...),
    title: str = None
):
    """Upload and process a presentation file"""
    
    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    file_extension = os.path.splitext(file.filename)[1].lower()
    if file_extension not in settings.ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400, 
            detail=f"File type {file_extension} not supported. Allowed: {', '.join(settings.ALLOWED_EXTENSIONS)}"
        )
    
    # Check file size
    if file.size > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=413, 
            detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE / (1024*1024)}MB"
        )
    
    try:
        # Generate unique presentation ID
        presentation_id = str(uuid.uuid4())
        
        # Create upload directory if it doesn't exist
        upload_dir = os.path.join(settings.UPLOAD_FOLDER, presentation_id)
        os.makedirs(upload_dir, exist_ok=True)
        
        # Save file
        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Create presentation record
        presentation = Presentation(
            presentation_id=presentation_id,
            title=title or file.filename,
            file_path=file_path,
            processing_status=PresentationStatus.PROCESSING
        )
        
        # Store in local storage
        storage = await get_storage()
        await storage.save_presentation(presentation.dict())
        
        # Trigger VLM analysis (async background task)
        asyncio.create_task(process_presentation_async(presentation_id, file_path))
        
        return PresentationUploadResponse(
            presentation_id=presentation_id,
            status="uploaded",
            message="Presentation uploaded successfully. Processing started."
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@router.get("/{presentation_id}")
async def get_presentation(presentation_id: str):
    """Get presentation details and processing status"""
    
    storage = await get_storage()
    presentation = await storage.get_presentation(presentation_id)
    
    if not presentation:
        raise HTTPException(status_code=404, detail="Presentation not found")
    
    return ApiResponse(
        success=True,
        message="Presentation retrieved successfully",
        data=presentation
    )

@router.get("/")
async def list_presentations(limit: int = 10, skip: int = 0):
    """List all presentations"""
    
    storage = await get_storage()
    presentations = await storage.list_presentations(limit=limit)
    
    return ApiResponse(
        success=True,
        message=f"Retrieved {len(presentations)} presentations",
        data={"presentations": presentations, "count": len(presentations)}
    )

@router.delete("/{presentation_id}")
async def delete_presentation(presentation_id: str):
    """Delete a presentation"""
    
    storage = await get_storage()
    presentation = await storage.get_presentation(presentation_id)
    
    if not presentation:
        raise HTTPException(status_code=404, detail="Presentation not found")
    
    try:
        # Delete files
        if os.path.exists(presentation["file_path"]):
            os.remove(presentation["file_path"])
            # Remove directory if empty
            upload_dir = os.path.dirname(presentation["file_path"])
            if os.path.exists(upload_dir) and not os.listdir(upload_dir):
                os.rmdir(upload_dir)
        
        return ApiResponse(
            success=True,
            message="Presentation deleted successfully"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Deletion failed: {str(e)}")

@router.get("/{presentation_id}/status")
async def get_processing_status(presentation_id: str):
    """Get current processing status of a presentation"""
    
    storage = await get_storage()
    presentation = await storage.get_presentation(presentation_id)
    
    if not presentation:
        raise HTTPException(status_code=404, detail="Presentation not found")
    
    return ApiResponse(
        success=True,
        message="Status retrieved successfully",
        data={
            "presentation_id": presentation_id,
            "status": presentation.get("processing_status", "unknown"),
            "total_slides": presentation.get("total_slides", 0),
            "upload_timestamp": presentation.get("upload_timestamp", "")
        }
    )