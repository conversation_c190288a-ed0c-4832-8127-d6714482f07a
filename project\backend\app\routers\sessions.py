from fastapi import APIRouter, HTTPException, Depends
from datetime import datetime
import uuid

from app.models.schemas import (
    SessionCreateRequest,
    SessionCreateResponse, 
    Session,
    SessionStatus,
    PresentationStateUpdate,
    ApiResponse
)
from app.services.local_storage import get_storage

router = APIRouter()

@router.post("/create", response_model=SessionCreateResponse)
async def create_session(request: SessionCreateRequest):
    """Create a new presentation session"""
    
    # Verify presentation exists
    storage = await get_storage()
    presentation = await storage.get_presentation(request.presentation_id)
    
    if not presentation:
        raise HTTPException(status_code=404, detail="Presentation not found")
    
    if presentation["processing_status"] != "ready":
        raise HTTPException(
            status_code=400, 
            detail="Presentation is not ready. Current status: " + presentation["processing_status"]
        )
    
    try:
        # Create new session
        session = Session(
            presentation_id=request.presentation_id,
            status=SessionStatus.CREATED
        )
        
        # Store session
        await storage.save_session(session.dict())
        
        # Store in cache for quick access
        await storage.cache_set(
            f"session:{session.session_id}",
            session.dict()
        )
        
        return SessionCreateResponse(
            session_id=session.session_id,
            status="created",
            message="Session created successfully"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Session creation failed: {str(e)}")

@router.get("/{session_id}")
async def get_session(session_id: str):
    """Get session details"""
    
    # Try Redis first for quick access
    redis_manager = await get_redis()
    session_data = await redis_manager.get_session(session_id)
    
    if session_data:
        return ApiResponse(
            success=True,
            message="Session retrieved from cache",
            data=session_data
        )
    
    # Fallback to database
    db = await get_database()
    session = await find_document("sessions", {"session_id": session_id})
    
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session.pop("_id", None)
    
    return ApiResponse(
        success=True,
        message="Session retrieved successfully",
        data=session
    )

@router.post("/{session_id}/start")
async def start_session(session_id: str):
    """Start a presentation session"""
    
    redis_manager = await get_redis()
    session_data = await redis_manager.get_session(session_id)
    
    if not session_data:
        raise HTTPException(status_code=404, detail="Session not found")
    
    if session_data["status"] != SessionStatus.CREATED:
        raise HTTPException(status_code=400, detail="Session cannot be started from current status")
    
    try:
        # Update session status
        updates = {
            "status": SessionStatus.ACTIVE,
            "start_time": datetime.utcnow().isoformat()
        }
        
        # Update Redis
        await redis_manager.update_session(session_id, updates)
        
        # Update database
        db = await get_database()
        await update_document(
            "sessions",
            {"session_id": session_id},
            updates
        )
        
        return ApiResponse(
            success=True,
            message="Session started successfully"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start session: {str(e)}")

@router.post("/{session_id}/pause")
async def pause_session(session_id: str):
    """Pause a presentation session"""
    
    redis_manager = await get_redis()
    session_data = await redis_manager.get_session(session_id)
    
    if not session_data:
        raise HTTPException(status_code=404, detail="Session not found")
    
    if session_data["status"] != SessionStatus.ACTIVE:
        raise HTTPException(status_code=400, detail="Only active sessions can be paused")
    
    try:
        updates = {"status": SessionStatus.PAUSED}
        
        await redis_manager.update_session(session_id, updates)
        
        db = await get_database()
        await update_document("sessions", {"session_id": session_id}, updates)
        
        return ApiResponse(
            success=True,
            message="Session paused successfully"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to pause session: {str(e)}")

@router.post("/{session_id}/resume")
async def resume_session(session_id: str):
    """Resume a paused session"""
    
    redis_manager = await get_redis()
    session_data = await redis_manager.get_session(session_id)
    
    if not session_data:
        raise HTTPException(status_code=404, detail="Session not found")
    
    if session_data["status"] != SessionStatus.PAUSED:
        raise HTTPException(status_code=400, detail="Only paused sessions can be resumed")
    
    try:
        updates = {"status": SessionStatus.ACTIVE}
        
        await redis_manager.update_session(session_id, updates)
        
        db = await get_database()
        await update_document("sessions", {"session_id": session_id}, updates)
        
        return ApiResponse(
            success=True,
            message="Session resumed successfully"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to resume session: {str(e)}")

@router.post("/{session_id}/end")
async def end_session(session_id: str):
    """End a presentation session"""
    
    redis_manager = await get_redis()
    session_data = await redis_manager.get_session(session_id)
    
    if not session_data:
        raise HTTPException(status_code=404, detail="Session not found")
    
    try:
        updates = {
            "status": SessionStatus.COMPLETED,
            "end_time": datetime.utcnow().isoformat()
        }
        
        await redis_manager.update_session(session_id, updates)
        
        db = await get_database()
        await update_document("sessions", {"session_id": session_id}, updates)
        
        return ApiResponse(
            success=True,
            message="Session ended successfully"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to end session: {str(e)}")

@router.post("/{session_id}/update-state")
async def update_presentation_state(session_id: str, state_update: PresentationStateUpdate):
    """Update presentation state during live session"""
    
    redis_manager = await get_redis()
    session_data = await redis_manager.get_session(session_id)
    
    if not session_data:
        raise HTTPException(status_code=404, detail="Session not found")
    
    if session_data["status"] != SessionStatus.ACTIVE:
        raise HTTPException(status_code=400, detail="Can only update state of active sessions")
    
    try:
        # Update current state
        current_state = session_data.get("current_state", {})
        
        if state_update.active_slide is not None:
            current_state["active_slide"] = state_update.active_slide
        
        if state_update.visible_blocks is not None:
            current_state["visible_blocks"] = state_update.visible_blocks
        
        if state_update.spotlight_position is not None:
            current_state["spotlight_position"] = state_update.spotlight_position
        
        if state_update.zoom_level is not None:
            current_state["zoom_level"] = state_update.zoom_level
        
        current_state["last_action_timestamp"] = datetime.utcnow().isoformat()
        
        # Update session data
        updates = {"current_state": current_state}
        await redis_manager.update_session(session_id, updates)
        
        return ApiResponse(
            success=True,
            message="Presentation state updated successfully",
            data={"current_state": current_state}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update state: {str(e)}")

@router.get("/{session_id}/state")
async def get_presentation_state(session_id: str):
    """Get current presentation state"""
    
    redis_manager = await get_redis()
    session_data = await redis_manager.get_session(session_id)
    
    if not session_data:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return ApiResponse(
        success=True,
        message="Presentation state retrieved successfully",
        data={"current_state": session_data.get("current_state", {})}
    )