from fastapi import APIRouter, HTTPException
from datetime import datetime

from app.models.schemas import (
    VoiceDetectionRequest,
    VoiceEvent,
    ApiResponse
)
from app.services.database import get_database, insert_document
from app.services.redis_manager import get_redis

router = APIRouter()

@router.post("/detect")
async def process_voice_detection(request: VoiceDetectionRequest):
    """Process a detected voice keyword and update content checklist"""
    
    # Verify session exists and is active
    redis_manager = await get_redis()
    session_data = await redis_manager.get_session(request.session_id)
    
    if not session_data:
        raise HTTPException(status_code=404, detail="Session not found")
    
    if session_data["status"] != "active":
        raise HTTPException(status_code=400, detail="Session is not active")
    
    try:
        # Create voice event
        voice_event = VoiceEvent(
            session_id=request.session_id,
            detected_keyword=request.detected_keyword,
            confidence_score=request.confidence_score,
            audio_duration=request.audio_duration
        )
        
        # Store in database
        db = await get_database()
        await insert_document("voice_events", voice_event.dict())
        
        # Process keyword matching
        match_result = await process_keyword_matching(voice_event, session_data)
        
        # Update session progress if keyword matches
        if match_result.get("matched"):
            await redis_manager.update_session(
                request.session_id,
                {"progress_update": match_result["progress_update"]}
            )
        
        return ApiResponse(
            success=True,
            message=f"Voice detection processed successfully",
            data={
                "voice_event_id": voice_event.voice_id,
                "keyword_matched": match_result.get("matched", False),
                "matching_key_point": match_result.get("key_point"),
                "confidence": request.confidence_score
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Voice processing failed: {str(e)}")

async def process_keyword_matching(voice_event: VoiceEvent, session_data: dict):
    """Match detected keyword against current slide's key points"""
    
    # Get current slide key points
    # This would normally fetch from the presentation data
    current_state = session_data.get("current_state", {})
    active_slide = current_state.get("active_slide", 0)
    
    # TODO: Implement actual keyword matching logic
    # For now, we'll simulate matching
    
    result = {
        "matched": False,
        "key_point": None,
        "progress_update": None
    }
    
    # Simple keyword matching simulation
    detected_keyword = voice_event.detected_keyword.lower()
    
    # This would be replaced with actual slide key points
    sample_keywords = ["introduction", "methodology", "results", "conclusion"]
    
    for keyword in sample_keywords:
        if keyword in detected_keyword:
            result["matched"] = True
            result["key_point"] = keyword
            result["progress_update"] = {
                "spoken_keywords": [keyword],
                "timestamp": datetime.utcnow().isoformat(),
                "slide_number": active_slide
            }
            break
    
    return result

@router.get("/{session_id}/keywords")
async def get_current_keywords(session_id: str):
    """Get current slide keywords for voice monitoring"""
    
    redis_manager = await get_redis()
    session_data = await redis_manager.get_session(session_id)
    
    if not session_data:
        raise HTTPException(status_code=404, detail="Session not found")
    
    # Get current slide number
    current_state = session_data.get("current_state", {})
    active_slide = current_state.get("active_slide", 0)
    
    # TODO: Fetch actual keywords from presentation data
    # For now, return sample keywords
    sample_keywords = [
        {"keyword": "introduction", "priority": "high"},
        {"keyword": "methodology", "priority": "medium"},
        {"keyword": "results", "priority": "high"},
        {"keyword": "conclusion", "priority": "medium"}
    ]
    
    return ApiResponse(
        success=True,
        message="Current slide keywords retrieved",
        data={
            "slide_number": active_slide,
            "keywords": sample_keywords
        }
    )

@router.get("/{session_id}/progress")
async def get_voice_progress(session_id: str):
    """Get current progress of spoken key points"""
    
    db = await get_database()
    
    # Get all voice events for this session
    voice_events = await db.voice_events.find(
        {"session_id": session_id}
    ).sort("timestamp", 1).to_list(length=None)
    
    # Calculate progress statistics
    total_events = len(voice_events)
    unique_keywords = len(set(event["detected_keyword"] for event in voice_events))
    
    # Group by slide (would need actual slide data)
    progress_by_slide = {}
    
    for event in voice_events:
        slide_num = 0  # Would be determined from timestamp and session state
        if slide_num not in progress_by_slide:
            progress_by_slide[slide_num] = []
        progress_by_slide[slide_num].append(event["detected_keyword"])
    
    return ApiResponse(
        success=True,
        message="Voice progress retrieved successfully",
        data={
            "total_voice_events": total_events,
            "unique_keywords_spoken": unique_keywords,
            "progress_by_slide": progress_by_slide,
            "recent_events": voice_events[-10:] if voice_events else []
        }
    )

@router.get("/{session_id}/history")
async def get_voice_history(session_id: str, limit: int = 50):
    """Get voice event history for a session"""
    
    db = await get_database()
    events = await db.voice_events.find(
        {"session_id": session_id}
    ).sort("timestamp", -1).limit(limit).to_list(length=None)
    
    # Remove MongoDB _id fields
    for event in events:
        event.pop("_id", None)
    
    return ApiResponse(
        success=True,
        message=f"Retrieved {len(events)} voice events",
        data={"events": events}
    )

@router.get("/{session_id}/stats")
async def get_voice_statistics(session_id: str):
    """Get voice detection statistics for a session"""
    
    db = await get_database()
    
    # Aggregate voice statistics
    pipeline = [
        {"$match": {"session_id": session_id}},
        {"$group": {
            "_id": "$detected_keyword",
            "count": {"$sum": 1},
            "avg_confidence": {"$avg": "$confidence_score"},
            "total_duration": {"$sum": "$audio_duration"},
            "last_detected": {"$max": "$timestamp"}
        }}
    ]
    
    stats = await db.voice_events.aggregate(pipeline).to_list(length=None)
    
    return ApiResponse(
        success=True,
        message="Voice statistics retrieved successfully",
        data={"statistics": stats}
    )