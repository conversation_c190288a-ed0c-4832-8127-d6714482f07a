"""
Authentication Routes for AI-Powered Presentation System
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional, Dict, Any
import logging
from datetime import timedelta

from ..models.auth_models import (
    UserRegister, UserLogin, AuthResponse, UserResponse, 
    Token, UserProfile
)
from ..services.mongodb_service import mongodb_service, ACCESS_TOKEN_EXPIRE_MINUTES

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/auth", tags=["authentication"])
security = HTTPBearer()

# Dependency to get current user
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Get current authenticated user from JWT token"""
    
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Verify token
        payload = mongodb_service.verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception
        
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
        
        # Get user from database
        user = await mongodb_service.get_user_by_email(email)
        if user is None:
            raise credentials_exception
        
        return user
        
    except Exception as e:
        logger.error(f"❌ Authentication error: {e}")
        raise credentials_exception

# Optional authentication dependency (for routes that work with or without auth)
async def get_current_user_optional(credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))) -> Optional[Dict[str, Any]]:
    """Get current user if authenticated, None otherwise"""
    if not credentials:
        return None
    
    try:
        payload = mongodb_service.verify_token(credentials.credentials)
        if payload is None:
            return None
        
        email: str = payload.get("sub")
        if email is None:
            return None
        
        user = await mongodb_service.get_user_by_email(email)
        return user
        
    except Exception:
        return None

@router.post("/register", response_model=AuthResponse)
async def register_user(user_data: UserRegister):
    """Register a new user"""
    try:
        # Create user in database
        user = await mongodb_service.create_user(
            email=user_data.email,
            username=user_data.username,
            password=user_data.password,
            full_name=user_data.full_name
        )
        
        # Create access token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = mongodb_service.create_access_token(
            data={"sub": user["email"]}, 
            expires_delta=access_token_expires
        )
        
        return AuthResponse(
            success=True,
            message="User registered successfully",
            user=UserResponse(**user),
            token=Token(access_token=access_token)
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ Registration failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )

@router.post("/login", response_model=AuthResponse)
async def login_user(user_credentials: UserLogin):
    """Authenticate user and return access token"""
    try:
        # Authenticate user
        user = await mongodb_service.authenticate_user(
            email=user_credentials.email,
            password=user_credentials.password
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password"
            )
        
        # Create access token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = mongodb_service.create_access_token(
            data={"sub": user["email"]}, 
            expires_delta=access_token_expires
        )
        
        logger.info(f"👤 User logged in: {user['username']} ({user['email']})")
        
        return AuthResponse(
            success=True,
            message="Login successful",
            user=UserResponse(**user),
            token=Token(access_token=access_token)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Login failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@router.get("/me", response_model=UserProfile)
async def get_current_user_profile(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Get current user profile"""
    return UserProfile(**current_user)

@router.post("/logout")
async def logout_user(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Logout user (client-side token removal)"""
    logger.info(f"👤 User logged out: {current_user['username']}")
    return {
        "success": True,
        "message": "Logged out successfully"
    }

@router.get("/verify")
async def verify_token(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Verify if token is valid"""
    return {
        "success": True,
        "message": "Token is valid",
        "user": UserResponse(**current_user)
    }
