"""
Voice Processing API Endpoints
Handles real-time voice monitoring and keyword detection
"""

from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect
from pydantic import BaseModel
from typing import List, Dict, Optional
import json
import logging
from datetime import datetime

from ..services.voice_processor import (
    initialize_voice_session,
    process_speech_input,
    get_voice_session_status,
    cleanup_voice_session
)

router = APIRouter(prefix="/api/voice", tags=["voice"])
logger = logging.getLogger(__name__)

# Pydantic models
class KeyPointRequest(BaseModel):
    id: str
    text: str
    slide_id: Optional[str] = None

class VoiceSessionRequest(BaseModel):
    session_id: str
    key_points: List[KeyPointRequest]

class SpeechInputRequest(BaseModel):
    session_id: str
    transcript: str
    confidence: Optional[float] = 1.0

class VoiceSettingsRequest(BaseModel):
    session_id: str
    sensitivity: Optional[float] = 0.6
    current_slide: Optional[str] = None

# Active WebSocket connections for real-time updates
active_connections: Dict[str, WebSocket] = {}

@router.post("/session/initialize")
async def initialize_session(request: VoiceSessionRequest):
    """Initialize a new voice monitoring session"""
    try:
        # Convert Pydantic models to dicts
        key_points_data = [
            {
                'id': kp.id,
                'text': kp.text,
                'slide_id': kp.slide_id or ''
            }
            for kp in request.key_points
        ]
        
        success = await initialize_voice_session(request.session_id, key_points_data)
        
        if success:
            return {
                "success": True,
                "session_id": request.session_id,
                "message": f"Voice session initialized with {len(key_points_data)} key points",
                "key_points_count": len(key_points_data)
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to initialize voice session")
    
    except Exception as e:
        logger.error(f"Error initializing voice session: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/process")
async def process_speech(request: SpeechInputRequest):
    """Process speech input and detect key points"""
    try:
        result = await process_speech_input(
            request.session_id,
            request.transcript,
            request.confidence
        )
        
        if "error" in result:
            raise HTTPException(status_code=404, detail=result["error"])
        
        # Send real-time update to connected WebSocket if available
        if request.session_id in active_connections:
            try:
                await active_connections[request.session_id].send_text(
                    json.dumps({
                        "type": "key_point_detection",
                        "data": result
                    })
                )
            except Exception as ws_error:
                logger.warning(f"Failed to send WebSocket update: {ws_error}")
                # Remove dead connection
                del active_connections[request.session_id]
        
        return result
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing speech input: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/session/{session_id}/status")
async def get_session_status(session_id: str):
    """Get current voice session status and progress"""
    try:
        status = await get_voice_session_status(session_id)
        
        if "error" in status:
            raise HTTPException(status_code=404, detail=status["error"])
        
        return status
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/session/settings")
async def update_session_settings(request: VoiceSettingsRequest):
    """Update voice session settings"""
    try:
        from ..services.voice_processor import voice_processor
        
        success = True
        updates = []
        
        # Update sensitivity threshold
        if request.sensitivity is not None:
            success &= await voice_processor.adjust_sensitivity(
                request.session_id, request.sensitivity
            )
            updates.append(f"sensitivity: {request.sensitivity}")
        
        # Update current slide
        if request.current_slide is not None:
            success &= await voice_processor.update_current_slide(
                request.session_id, request.current_slide
            )
            updates.append(f"current_slide: {request.current_slide}")
        
        if success:
            return {
                "success": True,
                "session_id": request.session_id,
                "updates": updates,
                "message": "Session settings updated successfully"
            }
        else:
            raise HTTPException(status_code=404, detail="Session not found")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating session settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/session/{session_id}")
async def cleanup_session(session_id: str):
    """Clean up voice session resources"""
    try:
        success = await cleanup_voice_session(session_id)
        
        # Clean up WebSocket connection
        if session_id in active_connections:
            del active_connections[session_id]
        
        if success:
            return {
                "success": True,
                "session_id": session_id,
                "message": "Voice session cleaned up successfully"
            }
        else:
            return {
                "success": False,
                "session_id": session_id,
                "message": "Session was already cleaned up or not found"
            }
    
    except Exception as e:
        logger.error(f"Error cleaning up session: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/session/{session_id}/events")
async def get_voice_events(session_id: str, limit: int = 50):
    """Get recent voice events for debugging"""
    try:
        from ..services.voice_processor import voice_processor
        
        events = await voice_processor.get_voice_events(session_id, limit)
        
        return {
            "session_id": session_id,
            "events": events,
            "count": len(events)
        }
    
    except Exception as e:
        logger.error(f"Error getting voice events: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.websocket("/session/{session_id}/ws")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time voice updates"""
    await websocket.accept()
    active_connections[session_id] = websocket
    
    try:
        logger.info(f"WebSocket connected for voice session: {session_id}")
        
        # Send initial status
        status = await get_voice_session_status(session_id)
        if "error" not in status:
            await websocket.send_text(json.dumps({
                "type": "session_status",
                "data": status
            }))
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client
                message = await websocket.receive_text()
                data = json.loads(message)
                
                # Handle different message types
                if data.get("type") == "speech_input":
                    result = await process_speech_input(
                        session_id,
                        data.get("transcript", ""),
                        data.get("confidence", 1.0)
                    )
                    
                    await websocket.send_text(json.dumps({
                        "type": "key_point_detection",
                        "data": result
                    }))
                
                elif data.get("type") == "get_status":
                    status = await get_voice_session_status(session_id)
                    await websocket.send_text(json.dumps({
                        "type": "session_status",
                        "data": status
                    }))
                
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"Error in WebSocket message handling: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": str(e)
                }))
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for voice session: {session_id}")
    except Exception as e:
        logger.error(f"WebSocket error for session {session_id}: {e}")
    finally:
        # Clean up connection
        if session_id in active_connections:
            del active_connections[session_id]

# Health check endpoint
@router.get("/health")
async def voice_health_check():
    """Health check for voice processing service"""
    return {
        "status": "healthy",
        "service": "voice_processor",
        "timestamp": datetime.now().isoformat(),
        "active_sessions": len(active_connections),
        "features": [
            "keyword_spotting",
            "fuzzy_matching",
            "real_time_processing",
            "websocket_support",
            "confidence_scoring"
        ]
    }