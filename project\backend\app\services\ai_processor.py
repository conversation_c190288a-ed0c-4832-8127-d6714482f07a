"""
Comprehensive AI Processing Service
Integrates Ollama Phi-3, offline VLM, document processing, and intelligent content analysis
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
import aiohttp
import torch
from PIL import Image
import fitz  # PyMuPDF for PDF processing
from pptx import Presentation
import io
import base64
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SlideContent:
    """Represents processed slide content"""
    slide_number: int
    title: str
    text_content: str
    key_points: List[str]
    semantic_blocks: List[Dict]
    image_data: Optional[str] = None
    image_url: Optional[str] = None
    complexity_score: float = 0.0
    estimated_duration: int = 60

@dataclass
class PresentationAnalysis:
    """Complete presentation analysis results"""
    presentation_id: str
    title: str
    total_slides: int
    slides: List[SlideContent]
    overall_themes: List[str]
    difficulty_level: str
    estimated_total_duration: int
    key_concepts: List[str]
    analysis_timestamp: datetime

class OllamaClient:
    """Async client for Ollama API communication"""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.model_name = "phi3:latest"
        
    async def test_connection(self) -> bool:
        """Test if Ollama is accessible"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/tags") as response:
                    if response.status == 200:
                        models = await response.json()
                        available_models = [model['name'] for model in models.get('models', [])]
                        logger.info(f"✅ Ollama connected. Available models: {available_models}")
                        return self.model_name in available_models
        except Exception as e:
            logger.error(f"❌ Ollama connection failed: {e}")
            return False
    
    async def generate_text(self, prompt: str, max_tokens: int = 500) -> Optional[str]:
        """Generate text using Ollama Phi-3"""
        try:
            # Set a reasonable timeout (e.g., 60 seconds) to prevent hanging
            timeout = aiohttp.ClientTimeout(total=60) 
            async with aiohttp.ClientSession(timeout=timeout) as session:
                payload = {
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "num_predict": max_tokens,
                        "temperature": 0.7,
                        "top_p": 0.9
                    }
                }
                
                logger.debug(f"📤 Sending AI request: {prompt[:50]}...")
                
                async with session.post(
                    f"{self.base_url}/api/generate",
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        response_text = result.get('response', '').strip()
                        logger.debug(f"📥 Received AI response: {response_text[:50]}...")
                        return response_text
                    else:
                        logger.error(f"❌ Ollama API error: {response.status}")
                        text = await response.text()
                        logger.error(f"Error details: {text}")
                        return None
        except asyncio.TimeoutError:
            logger.error("❌ AI request timed out after 60s")
            return None
        except Exception as e:
            logger.error(f"❌ Error generating text: {e}")
            return None

class DocumentProcessor:
    """Advanced document processing for presentations"""
    
    def __init__(self):
        self.supported_formats = {'.pdf', '.pptx', '.ppt'}
    
    async def extract_pdf_content(self, file_path: Path, presentation_id: str) -> List[Dict]:
        """Extract content from PDF files and render slides as images"""
        try:
            slides = []
            pdf_doc = fitz.open(file_path)
            
            # Ensure static directory exists for this presentation
            static_base = Path("static/presentations") / presentation_id
            static_base.mkdir(parents=True, exist_ok=True)
            
            for page_num in range(len(pdf_doc)):
                page = pdf_doc[page_num]
                
                # Extract text
                text = page.get_text()
                
                # Render full page as image
                pix = page.get_pixmap(matrix=fitz.Matrix(2, 2)) # 2x zoom for better quality
                image_filename = f"slide_{page_num + 1}.png"
                image_path = static_base / image_filename
                pix.save(str(image_path))
                
                # Generate URL (relative to server root)
                image_url = f"http://localhost:8000/static/presentations/{presentation_id}/{image_filename}"
                
                # Create slide representation
                slides.append({
                    'slide_number': page_num + 1,
                    'text_content': text,
                    'image_data': None, # Legacy field
                    'image_url': image_url, # New field for full slide image
                    'page_size': page.rect
                })
            
            pdf_doc.close()
            logger.info(f"✅ Extracted and rendered {len(slides)} slides from PDF")
            return slides
            
        except Exception as e:
            logger.error(f"Error processing PDF: {e}")
            return []
    
    async def extract_pptx_content(self, file_path: Path, presentation_id: str) -> List[Dict]:
        """Extract content from PowerPoint files"""
        try:
            slides = []
            prs = Presentation(file_path)
            
            # Note: PPTX rendering requires external tools (LibreOffice/Unoconv).
            # For now we extract text and placeholder for image.
            
            for slide_num, slide in enumerate(prs.slides):
                text_content = []
                
                # Extract text from all shapes
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        text_content.append(shape.text.strip())
                
                # Create slide representation
                slides.append({
                    'slide_number': slide_num + 1,
                    'text_content': '\n'.join(text_content),
                    'image_data': None,
                    'image_url': None, # TODO: Implement PPTX rendering if possible
                    'layout': slide.slide_layout.name if slide.slide_layout else 'Unknown'
                })
            
            logger.info(f"✅ Extracted {len(slides)} slides from PPTX")
            return slides
            
        except Exception as e:
            logger.error(f"Error processing PPTX: {e}")
            return []

class ContentAnalyzer:
    """Advanced content analysis using AI"""
    
    def __init__(self, ollama_client: OllamaClient):
        self.ollama = ollama_client
    
    async def extract_key_points(self, text: str) -> List[str]:
        """Extract key talking points from slide text"""
        if not text.strip():
            return []
        
        prompt = f"""
        Analyze this presentation slide text and extract the main key points that a presenter should cover.
        Return only the key points, one per line, without numbering or bullet points.
        Focus on the most important concepts, facts, or ideas.
        
        Slide text:
        {text}
        
        Key points:
        """
        
        response = await self.ollama.generate_text(prompt, max_tokens=300)
        if response:
            # Clean and filter the response
            points = [line.strip() for line in response.split('\n') if line.strip()]
            # Remove any numbering or bullet points
            cleaned_points = []
            for point in points:
                cleaned = re.sub(r'^[\d\.\-\*\•\►]\s*', '', point).strip()
                if cleaned and len(cleaned) > 10:  # Filter out very short points
                    cleaned_points.append(cleaned)
            return cleaned_points[:5]  # Limit to top 5 points
        
        return []
    
    async def generate_slide_title(self, text: str) -> str:
        """Generate or extract slide title"""
        if not text.strip():
            return "Untitled Slide"
        
        # First, try to find existing title (usually first line or largest text)
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        if lines:
            # If first line is short and looks like a title
            first_line = lines[0]
            if len(first_line) < 80 and not first_line.endswith('.'):
                return first_line
        
        # Generate title using AI
        prompt = f"""
        Create a concise, descriptive title for this presentation slide content.
        The title should be 3-8 words and capture the main topic.
        
        Slide content:
        {text[:500]}
        
        Title:
        """
        
        response = await self.ollama.generate_text(prompt, max_tokens=50)
        if response:
            title = response.split('\n')[0].strip()
            # Clean up the title
            title = re.sub(r'^["\'\-\*\•\►\d\.\s]*', '', title).strip()
            title = re.sub(r'["\'\-\*\•\►]*$', '', title).strip()
            if title and len(title) < 100:
                return title
        
        return "Slide Content"
    
    async def calculate_complexity_score(self, text: str) -> float:
        """Calculate content complexity score (0.0 - 1.0)"""
        if not text.strip():
            return 0.0
        
        # Simple heuristic-based complexity calculation
        word_count = len(text.split())
        sentence_count = len([s for s in text.split('.') if s.strip()])
        
        # Technical terms and complex words
        technical_terms = len(re.findall(r'\b[A-Z]{2,}|\b\w{10,}\b', text))
        
        # Calculate complexity factors
        avg_sentence_length = word_count / max(sentence_count, 1)
        technical_density = technical_terms / max(word_count, 1)
        
        # Normalize to 0-1 scale
        complexity = min(1.0, (avg_sentence_length / 20) + (technical_density * 2))
        return round(complexity, 2)
    
    async def estimate_presentation_duration(self, slides: List[SlideContent]) -> int:
        """Estimate total presentation duration in seconds"""
        total_duration = 0
        
        for slide in slides:
            word_count = len(slide.text_content.split())
            # Base time: 2 seconds per word for reading + complexity factor
            base_time = word_count * 2
            complexity_multiplier = 1 + slide.complexity_score
            slide_duration = int(base_time * complexity_multiplier)
            
            # Minimum 30 seconds, maximum 300 seconds per slide
            slide.estimated_duration = max(30, min(slide_duration, 300))
            total_duration += slide.estimated_duration
        
        return total_duration
    
    async def extract_overall_themes(self, all_text: str) -> List[str]:
        """Extract main themes from entire presentation"""
        if not all_text.strip():
            return []
        
        prompt = f"""
        Analyze this complete presentation content and identify the main themes or topics.
        Return 3-5 main themes, each as a single phrase or short sentence.
        Focus on the overarching concepts that tie the presentation together.
        
        Presentation content:
        {all_text[:2000]}...
        
        Main themes:
        """
        
        response = await self.ollama.generate_text(prompt, max_tokens=200)
        if response:
            themes = [line.strip() for line in response.split('\n') if line.strip()]
            cleaned_themes = []
            for theme in themes:
                cleaned = re.sub(r'^[\d\.\-\*\•\►]\s*', '', theme).strip()
                if cleaned and len(cleaned) > 5:
                    cleaned_themes.append(cleaned)
            return cleaned_themes[:5]
        
        return []

class AIProcessor:
    """Main AI processing service coordinating all components"""
    
    def __init__(self):
        self.ollama_client = OllamaClient()
        self.document_processor = DocumentProcessor()
        self.content_analyzer = ContentAnalyzer(self.ollama_client)
        self.is_initialized = False
    
    async def initialize(self) -> bool:
        """Initialize AI services and check connectivity"""
        try:
            # Test Ollama connection
            ollama_available = await self.ollama_client.test_connection()
            
            if ollama_available:
                logger.info("🤖 AI Processor initialized successfully")
                self.is_initialized = True
                return True
            else:
                logger.warning("⚠️ Ollama not available, using fallback methods")
                self.is_initialized = True  # Still usable without AI
                return False
        except Exception as e:
            logger.error(f"❌ AI Processor initialization failed: {e}")
            return False
    
    async def process_presentation(
        self, 
        file_path: Path, 
        presentation_id: str,
        title: Optional[str] = None
    ) -> Optional[PresentationAnalysis]:
        """Complete presentation processing pipeline"""
        
        if not self.is_initialized:
            await self.initialize()
        
        try:
            # Extract document content based on file type
            file_extension = file_path.suffix.lower()
            
            if file_extension == '.pdf':
                raw_slides = await self.document_processor.extract_pdf_content(file_path, presentation_id)
            elif file_extension in ['.pptx', '.ppt']:
                raw_slides = await self.document_processor.extract_pptx_content(file_path, presentation_id)
            else:
                logger.error(f"Unsupported file format: {file_extension}")
                return None
            
            if not raw_slides:
                logger.error("No content extracted from document")
                return None
            
            # Process slides in parallel with concurrency limit
            semaphore = asyncio.Semaphore(3) # Limit to 3 concurrent slides
            
            async def process_single_slide(raw_slide):
                async with semaphore:
                    try:
                        slide_text = raw_slide['text_content']
                        
                        # run analysis tasks in parallel
                        title_task = self.content_analyzer.generate_slide_title(slide_text)
                        kp_task = self.content_analyzer.extract_key_points(slide_text)
                        complexity_task = self.content_analyzer.calculate_complexity_score(slide_text)
                        
                        slide_title, key_points, complexity = await asyncio.gather(
                            title_task, kp_task, complexity_task
                        )
                        
                        # Create semantic blocks
                        semantic_blocks = []
                        if slide_title != "Untitled Slide":
                            semantic_blocks.append({
                                'type': 'title',
                                'content': slide_title,
                                'coordinates': {'x': 50, 'y': 50, 'width': 700, 'height': 100}
                            })
                        
                        if slide_text.strip():
                            semantic_blocks.append({
                                'type': 'content',
                                'content': slide_text[:200] + "..." if len(slide_text) > 200 else slide_text,
                                'coordinates': {'x': 50, 'y': 200, 'width': 700, 'height': 400}
                            })
                        
                        slide_content = SlideContent(
                            slide_number=raw_slide['slide_number'],
                            title=slide_title,
                            text_content=slide_text,
                            key_points=key_points,
                            semantic_blocks=semantic_blocks,
                            image_data=raw_slide.get('image_data'),
                            image_url=raw_slide.get('image_url'),
                            complexity_score=complexity
                        )
                        logger.info(f"✅ Processed slide {slide_content.slide_number}: {slide_title}")
                        return slide_content
                    except Exception as e:
                        logger.error(f"Error processing slide {raw_slide.get('slide_number')}: {e}")
                        # Return a fallback slide content
                        return SlideContent(
                            slide_number=raw_slide['slide_number'],
                            title="Error Processing Slide",
                            text_content=raw_slide['text_content'],
                            key_points=["Error generating key points"],
                            semantic_blocks=[],
                            complexity_score=0.0
                        )

            processed_slides = await asyncio.gather(*[process_single_slide(s) for s in raw_slides])
            processed_slides.sort(key=lambda s: s.slide_number)
            
            # Collect all text for theme analysis
            all_text = "\n".join([s.text_content for s in processed_slides])
            
            # Estimate durations
            total_duration = await self.content_analyzer.estimate_presentation_duration(processed_slides)
            
            # Extract overall themes and concepts
            themes = await self.content_analyzer.extract_overall_themes(all_text)
            
            # Determine difficulty level based on average complexity
            avg_complexity = sum(slide.complexity_score for slide in processed_slides) / len(processed_slides)
            if avg_complexity < 0.3:
                difficulty = "Beginner"
            elif avg_complexity < 0.7:
                difficulty = "Intermediate"
            else:
                difficulty = "Advanced"
            
            # Create final analysis
            analysis = PresentationAnalysis(
                presentation_id=presentation_id,
                title=title or file_path.stem,
                total_slides=len(processed_slides),
                slides=processed_slides,
                overall_themes=themes,
                difficulty_level=difficulty,
                estimated_total_duration=total_duration,
                key_concepts=themes[:3],  # Use themes as key concepts
                analysis_timestamp=datetime.now()
            )
            
            logger.info(f"🎉 Presentation analysis completed: {len(processed_slides)} slides, {difficulty} level")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ Presentation processing failed: {e}")
            return None
    
    async def get_presentation_insights(self, analysis: PresentationAnalysis) -> Dict[str, Any]:
        """Generate insights and recommendations for the presentation"""
        
        insights = {
            'overview': {
                'total_slides': analysis.total_slides,
                'estimated_duration_minutes': round(analysis.estimated_total_duration / 60, 1),
                'difficulty_level': analysis.difficulty_level,
                'main_themes': analysis.overall_themes
            },
            'slide_breakdown': [],
            'recommendations': [],
            'key_statistics': {}
        }
        
        # Slide-by-slide breakdown
        for slide in analysis.slides:
            insights['slide_breakdown'].append({
                'slide_number': slide.slide_number,
                'title': slide.title,
                'key_points_count': len(slide.key_points),
                'complexity_score': slide.complexity_score,
                'estimated_duration_seconds': slide.estimated_duration
            })
        
        # Generate recommendations
        avg_complexity = sum(slide.complexity_score for slide in analysis.slides) / len(analysis.slides)
        
        if avg_complexity > 0.7:
            insights['recommendations'].append("Consider simplifying complex slides for better audience comprehension")
        
        if analysis.estimated_total_duration > 1800:  # 30 minutes
            insights['recommendations'].append("Presentation might be too long - consider splitting into multiple sessions")
        
        if any(len(slide.key_points) > 5 for slide in analysis.slides):
            insights['recommendations'].append("Some slides have too many key points - consider reducing for clarity")
        
        # Key statistics
        insights['key_statistics'] = {
            'average_complexity': round(avg_complexity, 2),
            'total_key_points': sum(len(slide.key_points) for slide in analysis.slides),
            'slides_with_images': sum(1 for slide in analysis.slides if slide.image_data),
            'longest_slide_duration': max(slide.estimated_duration for slide in analysis.slides),
            'shortest_slide_duration': min(slide.estimated_duration for slide in analysis.slides)
        }
        
        return insights

# Global AI processor instance
ai_processor = AIProcessor()

# Convenience functions for FastAPI integration
async def initialize_ai_services() -> bool:
    """Initialize AI processing services"""
    return await ai_processor.initialize()

async def process_uploaded_presentation(
    file_path: Path, 
    presentation_id: str, 
    title: Optional[str] = None
) -> Optional[PresentationAnalysis]:
    """Process uploaded presentation file"""
    return await ai_processor.process_presentation(file_path, presentation_id, title)

async def get_ai_insights(analysis: PresentationAnalysis) -> Dict[str, Any]:
    """Get AI-powered insights for presentation"""
    return await ai_processor.get_presentation_insights(analysis)