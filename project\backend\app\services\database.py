from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ConnectionFailure
import logging
from app.utils.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)

class Database:
    client: AsyncIOMotorClient = None
    database = None

db = Database()

async def get_database():
    return db.database

async def init_database():
    """Initialize database connection"""
    try:
        db.client = AsyncIOMotorClient(settings.MONGODB_URL)
        db.database = db.client[settings.DATABASE_NAME]
        
        # Test connection
        await db.client.admin.command('ping')
        logger.info("✅ Successfully connected to MongoDB")
        
        # Create indexes for better performance
        await create_indexes()
        
    except ConnectionFailure as e:
        logger.error(f"❌ Failed to connect to MongoDB: {e}")
        raise

async def close_database():
    """Close database connection"""
    if db.client:
        db.client.close()
        logger.info("🔌 Database connection closed")

async def create_indexes():
    """Create database indexes for optimized queries"""
    try:
        # Presentations collection indexes
        await db.database.presentations.create_index("presentation_id", unique=True)
        await db.database.presentations.create_index("upload_timestamp")
        
        # Sessions collection indexes
        await db.database.sessions.create_index("session_id", unique=True)
        await db.database.sessions.create_index("presentation_id")
        await db.database.sessions.create_index("start_time")
        
        # Gesture events indexes
        await db.database.gesture_events.create_index([("session_id", 1), ("timestamp", -1)])
        await db.database.gesture_events.create_index("gesture_type")
        
        # Voice events indexes
        await db.database.voice_events.create_index([("session_id", 1), ("timestamp", -1)])
        await db.database.voice_events.create_index("detected_keyword")
        
        logger.info("📊 Database indexes created successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to create indexes: {e}")

# Database operation helpers
async def insert_document(collection: str, document: dict):
    """Insert a document into specified collection"""
    try:
        result = await db.database[collection].insert_one(document)
        return str(result.inserted_id)
    except Exception as e:
        logger.error(f"❌ Failed to insert document: {e}")
        raise

async def find_document(collection: str, filter_dict: dict):
    """Find a single document"""
    try:
        document = await db.database[collection].find_one(filter_dict)
        return document
    except Exception as e:
        logger.error(f"❌ Failed to find document: {e}")
        raise

async def find_documents(collection: str, filter_dict: dict = None, limit: int = None):
    """Find multiple documents"""
    try:
        cursor = db.database[collection].find(filter_dict or {})
        if limit:
            cursor = cursor.limit(limit)
        documents = await cursor.to_list(length=None)
        return documents
    except Exception as e:
        logger.error(f"❌ Failed to find documents: {e}")
        raise

async def update_document(collection: str, filter_dict: dict, update_dict: dict):
    """Update a document"""
    try:
        result = await db.database[collection].update_one(
            filter_dict, 
            {"$set": update_dict}
        )
        return result.modified_count > 0
    except Exception as e:
        logger.error(f"❌ Failed to update document: {e}")
        raise

async def delete_document(collection: str, filter_dict: dict):
    """Delete a document"""
    try:
        result = await db.database[collection].delete_one(filter_dict)
        return result.deleted_count > 0
    except Exception as e:
        logger.error(f"❌ Failed to delete document: {e}")
        raise