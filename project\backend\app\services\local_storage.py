import os
import json
import sqlite3
import aiofiles
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

from app.utils.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)

class LocalStorage:
    """Local file system and SQLite storage manager"""
    
    def __init__(self):
        self.data_dir = Path(settings.DATA_DIR)
        self.presentations_dir = Path(settings.PRESENTATIONS_DIR)
        self.sessions_dir = Path(settings.SESSIONS_DIR)
        self.uploads_dir = Path(settings.UPLOADS_DIR)
        self.db_path = settings.DATABASE_URL.replace("sqlite:///", "")
        
    async def initialize(self):
        """Initialize storage directories and database"""
        try:
            # Create directories
            for directory in [self.data_dir, self.presentations_dir, self.sessions_dir, self.uploads_dir]:
                directory.mkdir(parents=True, exist_ok=True)
            
            # Initialize SQLite database
            await self._init_database()
            
            logger.info("✅ Local storage initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize local storage: {e}")
            raise
    
    async def _init_database(self):
        """Initialize SQLite database with required tables"""
        connection = sqlite3.connect(self.db_path)
        cursor = connection.cursor()
        
        # Presentations table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS presentations (
                presentation_id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                file_path TEXT NOT NULL,
                upload_timestamp TEXT NOT NULL,
                processing_status TEXT NOT NULL,
                total_slides INTEGER DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Sessions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sessions (
                session_id TEXT PRIMARY KEY,
                presentation_id TEXT NOT NULL,
                status TEXT NOT NULL,
                start_time TEXT NOT NULL,
                end_time TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (presentation_id) REFERENCES presentations (presentation_id)
            )
        """)
        
        # Gesture events table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS gesture_events (
                event_id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                gesture_type TEXT NOT NULL,
                confidence_score REAL NOT NULL,
                coordinates TEXT,
                timestamp TEXT NOT NULL,
                FOREIGN KEY (session_id) REFERENCES sessions (session_id)
            )
        """)
        
        # Voice events table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS voice_events (
                voice_id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                detected_keyword TEXT NOT NULL,
                confidence_score REAL NOT NULL,
                audio_duration REAL NOT NULL,
                timestamp TEXT NOT NULL,
                FOREIGN KEY (session_id) REFERENCES sessions (session_id)
            )
        """)
        
        connection.commit()
        connection.close()
    
    # Presentation operations
    async def save_presentation(self, presentation_data: Dict[str, Any]) -> str:
        """Save presentation metadata to SQLite and full data to JSON"""
        presentation_id = presentation_data['presentation_id']
        
        # Save metadata to SQLite
        connection = sqlite3.connect(self.db_path)
        cursor = connection.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO presentations 
            (presentation_id, title, file_path, upload_timestamp, processing_status, total_slides)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            presentation_id,
            presentation_data['title'],
            presentation_data['file_path'],
            presentation_data['upload_timestamp'],
            presentation_data['processing_status'],
            presentation_data.get('total_slides', 0)
        ))
        
        connection.commit()
        connection.close()
        
        # Save full data to JSON file
        json_file_path = self.presentations_dir / f"{presentation_id}.json"
        async with aiofiles.open(json_file_path, 'w') as f:
            await f.write(json.dumps(presentation_data, default=str, indent=2))
        
        logger.info(f"💾 Presentation {presentation_id} saved locally")
        return presentation_id
    
    async def get_presentation(self, presentation_id: str) -> Optional[Dict[str, Any]]:
        """Get presentation data from JSON file"""
        json_file_path = self.presentations_dir / f"{presentation_id}.json"
        
        if not json_file_path.exists():
            return None
        
        async with aiofiles.open(json_file_path, 'r') as f:
            content = await f.read()
            return json.loads(content)
    
    async def list_presentations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """List presentations from SQLite"""
        connection = sqlite3.connect(self.db_path)
        cursor = connection.cursor()
        
        cursor.execute("""
            SELECT presentation_id, title, upload_timestamp, processing_status, total_slides
            FROM presentations 
            ORDER BY upload_timestamp DESC 
            LIMIT ?
        """, (limit,))
        
        rows = cursor.fetchall()
        connection.close()
        
        presentations = []
        for row in rows:
            presentations.append({
                'presentation_id': row[0],
                'title': row[1],
                'upload_timestamp': row[2],
                'processing_status': row[3],
                'total_slides': row[4]
            })
        
        return presentations
    
    async def update_presentation_status(self, presentation_id: str, status: str):
        """Update presentation processing status"""
        connection = sqlite3.connect(self.db_path)
        cursor = connection.cursor()
        
        cursor.execute("""
            UPDATE presentations 
            SET processing_status = ?
            WHERE presentation_id = ?
        """, (status, presentation_id))
        
        connection.commit()
        connection.close()
    
    # Session operations
    async def save_session(self, session_data: Dict[str, Any]) -> str:
        """Save session data"""
        session_id = session_data['session_id']
        
        # Save metadata to SQLite
        connection = sqlite3.connect(self.db_path)
        cursor = connection.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO sessions 
            (session_id, presentation_id, status, start_time, end_time)
            VALUES (?, ?, ?, ?, ?)
        """, (
            session_id,
            session_data['presentation_id'],
            session_data['status'],
            session_data['start_time'],
            session_data.get('end_time')
        ))
        
        connection.commit()
        connection.close()
        
        # Save full session state to JSON
        json_file_path = self.sessions_dir / f"{session_id}.json"
        async with aiofiles.open(json_file_path, 'w') as f:
            await f.write(json.dumps(session_data, default=str, indent=2))
        
        return session_id
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data"""
        json_file_path = self.sessions_dir / f"{session_id}.json"
        
        if not json_file_path.exists():
            return None
        
        async with aiofiles.open(json_file_path, 'r') as f:
            content = await f.read()
            return json.loads(content)
    
    async def update_session(self, session_id: str, updates: Dict[str, Any]):
        """Update session data"""
        session_data = await self.get_session(session_id)
        if session_data:
            session_data.update(updates)
            await self.save_session(session_data)
    
    # Event logging
    async def log_gesture_event(self, event_data: Dict[str, Any]):
        """Log gesture event to SQLite"""
        connection = sqlite3.connect(self.db_path)
        cursor = connection.cursor()
        
        cursor.execute("""
            INSERT INTO gesture_events 
            (event_id, session_id, gesture_type, confidence_score, coordinates, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            event_data['event_id'],
            event_data['session_id'],
            event_data['gesture_type'],
            event_data['confidence_score'],
            json.dumps(event_data.get('coordinates')),
            event_data['timestamp']
        ))
        
        connection.commit()
        connection.close()
    
    async def log_voice_event(self, event_data: Dict[str, Any]):
        """Log voice event to SQLite"""
        connection = sqlite3.connect(self.db_path)
        cursor = connection.cursor()
        
        cursor.execute("""
            INSERT INTO voice_events 
            (voice_id, session_id, detected_keyword, confidence_score, audio_duration, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            event_data['voice_id'],
            event_data['session_id'],
            event_data['detected_keyword'],
            event_data['confidence_score'],
            event_data['audio_duration'],
            event_data['timestamp']
        ))
        
        connection.commit()
        connection.close()
    
    # File operations
    async def save_file(self, file_content: bytes, file_path: str) -> str:
        """Save uploaded file"""
        full_path = self.uploads_dir / file_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        async with aiofiles.open(full_path, 'wb') as f:
            await f.write(file_content)
        
        return str(full_path)
    
    async def get_file_path(self, filename: str) -> str:
        """Get full file path"""
        return str(self.uploads_dir / filename)
    
    # Cache operations (in-memory for now, can be file-based)
    _cache: Dict[str, Any] = {}
    
    async def cache_set(self, key: str, value: Any, ttl: int = 3600):
        """Set cache value (simple in-memory implementation)"""
        self._cache[key] = {
            'value': value,
            'expires': datetime.now().timestamp() + ttl
        }
    
    async def cache_get(self, key: str) -> Optional[Any]:
        """Get cache value"""
        if key in self._cache:
            cache_entry = self._cache[key]
            if datetime.now().timestamp() < cache_entry['expires']:
                return cache_entry['value']
            else:
                del self._cache[key]
        return None
    
    async def cache_delete(self, key: str):
        """Delete cache value"""
        self._cache.pop(key, None)

# Global storage instance
local_storage = LocalStorage()

async def init_local_storage():
    """Initialize local storage"""
    await local_storage.initialize()

async def get_storage() -> LocalStorage:
    """Get storage instance"""
    return local_storage