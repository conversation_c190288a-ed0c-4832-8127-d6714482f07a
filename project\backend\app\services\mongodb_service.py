"""
MongoDB Service for AI-Powered Presentation System
Handles database connections and operations with user authentication
"""

import os
import logging
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import Duplicate<PERSON>eyError
from bson import ObjectId
from passlib.context import CryptContext
from jose import JWTError, jwt

logger = logging.getLogger(__name__)

# Password hashing - using pbkdf2_sha256 as fallback for bcrypt issues
pwd_context = CryptContext(schemes=["pbkdf2_sha256"], deprecated="auto")

# JWT settings
SECRET_KEY = "your-secret-key-change-this-in-production"  # Change this in production
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

class MongoDBService:
    """MongoDB service for user authentication and data management"""
    
    def __init__(self, connection_string: str = None):
        self.connection_string = connection_string or os.getenv("MONGODB_URL") or "mongodb+srv://majproject:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
        self.db_name = "ai_presentation_system"
        
    async def connect(self):
        """Connect to MongoDB"""
        try:
            self.client = AsyncIOMotorClient(self.connection_string)
            self.database = self.client[self.db_name]
            
            # Test the connection
            await self.client.admin.command('ping')
            logger.info("✅ Connected to MongoDB successfully")
            
            # Create indexes
            await self._create_indexes()
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to MongoDB: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from MongoDB"""
        if self.client:
            self.client.close()
            logger.info("📤 Disconnected from MongoDB")
    
    async def _create_indexes(self):
        """Create necessary database indexes"""
        try:
            # Users collection indexes
            await self.database.users.create_index("email", unique=True)
            await self.database.users.create_index("username", unique=True)
            
            # Presentations collection indexes
            await self.database.presentations.create_index("user_id")
            await self.database.presentations.create_index("created_at")
            
            # Analysis collection indexes
            await self.database.analysis.create_index("user_id")
            await self.database.analysis.create_index("presentation_id")
            
            logger.info("✅ Database indexes created successfully")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to create indexes: {e}")
    
    # User Authentication Methods
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """Hash a password"""
        return pwd_context.hash(password)
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None):
        """Create a JWT access token"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify and decode a JWT token"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            return payload
        except JWTError:
            return None
    
    async def create_user(self, email: str, username: str, password: str, full_name: str = None) -> Dict[str, Any]:
        """Create a new user"""
        try:
            # Check if user already exists
            existing_user = await self.database.users.find_one({
                "$or": [{"email": email}, {"username": username}]
            })
            
            if existing_user:
                if existing_user["email"] == email:
                    raise ValueError("Email already registered")
                else:
                    raise ValueError("Username already taken")

            # Validate password length for bcrypt (max 72 bytes)
            if len(password.encode('utf-8')) > 72:
                raise ValueError("Password too long. Maximum 72 characters allowed.")

            # Create user document
            user_doc = {
                "email": email,
                "username": username,
                "full_name": full_name or username,
                "hashed_password": self.get_password_hash(password),
                "created_at": datetime.utcnow(),
                "is_active": True,
                "presentation_count": 0
            }
            
            result = await self.database.users.insert_one(user_doc)
            
            # Return user without password
            user_doc["_id"] = str(result.inserted_id)
            del user_doc["hashed_password"]
            
            logger.info(f"👤 User created: {username} ({email})")
            return user_doc
            
        except DuplicateKeyError:
            raise ValueError("User already exists")
        except Exception as e:
            logger.error(f"❌ Failed to create user: {e}")
            raise
    
    async def authenticate_user(self, email: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate a user with email and password"""
        try:
            user = await self.database.users.find_one({"email": email})
            
            if not user:
                return None
            
            if not self.verify_password(password, user["hashed_password"]):
                return None
            
            # Convert ObjectId to string and remove password
            user["_id"] = str(user["_id"])
            del user["hashed_password"]
            
            return user
            
        except Exception as e:
            logger.error(f"❌ Authentication failed: {e}")
            return None
    
    async def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user by ID"""
        try:
            user = await self.database.users.find_one({"_id": ObjectId(user_id)})
            
            if user:
                user["_id"] = str(user["_id"])
                del user["hashed_password"]
                return user
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get user: {e}")
            return None
    
    async def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user by email"""
        try:
            user = await self.database.users.find_one({"email": email})
            
            if user:
                user["_id"] = str(user["_id"])
                del user["hashed_password"]
                return user
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get user by email: {e}")
            return None

# Global instance
mongodb_service = MongoDBService()
