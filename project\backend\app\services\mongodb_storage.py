"""
MongoDB Storage Service with User Authentication
Replaces SimpleStorage with MongoDB operations that include user isolation
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from bson import ObjectId
from .mongodb_service import mongodb_service

logger = logging.getLogger(__name__)

class MongoDBStorage:
    """MongoDB storage manager with user authentication and data isolation"""
    
    def __init__(self):
        self.db = None
    
    async def initialize(self):
        """Initialize MongoDB connection"""
        try:
            await mongodb_service.connect()
            self.db = mongodb_service.database
            logger.info("✅ MongoDB storage initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize MongoDB storage: {e}")
            raise
    
    # Presentation operations with user isolation
    
    async def save_presentation(self, presentation_id: str, data: Dict[str, Any], user_id: str) -> None:
        """Save presentation data with user association"""
        try:
            # Filter out _id field to avoid immutable field error
            filtered_data = {k: v for k, v in data.items() if k != "_id"}

            # Add user_id and metadata
            presentation_data = {
                **filtered_data,
                "user_id": user_id,
                "presentation_id": presentation_id,
                "updated_at": datetime.utcnow()
            }
            
            # Upsert the presentation
            await self.db.presentations.update_one(
                {"presentation_id": presentation_id, "user_id": user_id},
                {"$set": presentation_data},
                upsert=True
            )
            
            # Update user's presentation count
            await self.db.users.update_one(
                {"_id": ObjectId(user_id)},
                {"$inc": {"presentation_count": 1}}
            )
            
            logger.info(f"💾 Presentation {presentation_id} saved for user {user_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save presentation {presentation_id}: {e}")
            raise
    
    async def get_presentation(self, presentation_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get presentation data for specific user"""
        try:
            presentation = await self.db.presentations.find_one({
                "presentation_id": presentation_id,
                "user_id": user_id
            })
            
            if presentation:
                # Convert ObjectId to string
                presentation["_id"] = str(presentation["_id"])
                return presentation
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get presentation {presentation_id}: {e}")
            return None
    
    async def list_presentations(self, user_id: str) -> List[Dict[str, Any]]:
        """List all presentations for a specific user"""
        try:
            presentations = []
            
            cursor = self.db.presentations.find({"user_id": user_id}).sort("upload_timestamp", -1)
            
            async for presentation in cursor:
                presentation["_id"] = str(presentation["_id"])
                presentations.append({
                    'presentation_id': presentation.get('presentation_id'),
                    'title': presentation.get('title'),
                    'status': presentation.get('status'),
                    'upload_timestamp': presentation.get('upload_timestamp'),
                    'total_slides': presentation.get('total_slides', 0)
                })
            
            return presentations
            
        except Exception as e:
            logger.error(f"❌ Failed to list presentations for user {user_id}: {e}")
            return []

    async def get_total_presentations_count(self) -> int:
        """Get total count of all presentations (for health check)"""
        try:
            count = await self.db.presentations.count_documents({})
            return count
        except Exception as e:
            logger.error(f"❌ Failed to get presentations count: {e}")
            return 0

    async def delete_presentation(self, presentation_id: str, user_id: str) -> bool:
        """Delete presentation for specific user"""
        try:
            # Delete presentation
            result = await self.db.presentations.delete_one({
                "presentation_id": presentation_id,
                "user_id": user_id
            })
            
            if result.deleted_count > 0:
                # Also delete associated analysis
                await self.db.analysis.delete_one({
                    "presentation_id": presentation_id,
                    "user_id": user_id
                })
                
                # Update user's presentation count
                await self.db.users.update_one(
                    {"_id": ObjectId(user_id)},
                    {"$inc": {"presentation_count": -1}}
                )
                
                logger.info(f"🗑️ Presentation {presentation_id} deleted for user {user_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Failed to delete presentation {presentation_id}: {e}")
            return False
    
    # Analysis operations with user isolation
    
    async def save_analysis(self, presentation_id: str, analysis_data: Dict[str, Any], user_id: str) -> None:
        """Save AI analysis data with user association"""
        try:
            # Filter out _id field to avoid immutable field error
            filtered_analysis_data = {k: v for k, v in analysis_data.items() if k != "_id"}

            # Add user_id and metadata
            analysis_doc = {
                **filtered_analysis_data,
                "user_id": user_id,
                "presentation_id": presentation_id,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            # Upsert the analysis
            await self.db.analysis.update_one(
                {"presentation_id": presentation_id, "user_id": user_id},
                {"$set": analysis_doc},
                upsert=True
            )
            
            logger.info(f"📊 Analysis for {presentation_id} saved for user {user_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save analysis for {presentation_id}: {e}")
            raise
    
    async def get_analysis(self, presentation_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get AI analysis data for specific user"""
        try:
            analysis = await self.db.analysis.find_one({
                "presentation_id": presentation_id,
                "user_id": user_id
            })
            
            if analysis:
                # Convert ObjectId to string
                analysis["_id"] = str(analysis["_id"])
                return analysis
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get analysis for {presentation_id}: {e}")
            return None
    
    # Session operations with user isolation
    
    async def save_session(self, session_id: str, session_data: Dict[str, Any], user_id: str) -> None:
        """Save session data with user association"""
        try:
            # Add user_id and metadata
            session_doc = {
                **session_data,
                "user_id": user_id,
                "session_id": session_id,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            # Upsert the session
            await self.db.sessions.update_one(
                {"session_id": session_id, "user_id": user_id},
                {"$set": session_doc},
                upsert=True
            )
            
            logger.info(f"🎭 Session {session_id} saved for user {user_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save session {session_id}: {e}")
            raise
    
    async def get_session(self, session_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get session data for specific user"""
        try:
            session = await self.db.sessions.find_one({
                "session_id": session_id,
                "user_id": user_id
            })
            
            if session:
                # Convert ObjectId to string
                session["_id"] = str(session["_id"])
                return session
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get session {session_id}: {e}")
            return None
    
    # User statistics
    
    async def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """Get user statistics"""
        try:
            # Count presentations
            presentation_count = await self.db.presentations.count_documents({"user_id": user_id})
            
            # Count sessions
            session_count = await self.db.sessions.count_documents({"user_id": user_id})
            
            # Get recent activity
            recent_presentations = await self.db.presentations.find(
                {"user_id": user_id}
            ).sort("upload_timestamp", -1).limit(5).to_list(length=5)
            
            return {
                "presentation_count": presentation_count,
                "session_count": session_count,
                "recent_presentations": [
                    {
                        "presentation_id": p.get("presentation_id"),
                        "title": p.get("title"),
                        "upload_timestamp": p.get("upload_timestamp")
                    }
                    for p in recent_presentations
                ]
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get user stats for {user_id}: {e}")
            return {
                "presentation_count": 0,
                "session_count": 0,
                "recent_presentations": []
            }

# Global instance
mongodb_storage = MongoDBStorage()
