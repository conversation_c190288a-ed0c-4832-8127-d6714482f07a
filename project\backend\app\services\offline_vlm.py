import asyncio
import json
import logging
import io
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# Basic imports that should always work
import re

# Optional heavy dependencies
try:
    import fitz  # PyMuPDF
    FITZ_AVAILABLE = True
except ImportError:
    FITZ_AVAILABLE = False
    
try:
    from pptx import Presentation as PPTXPresentation
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from app.utils.config import get_settings
from app.services.local_storage import get_storage

settings = get_settings()
logger = logging.getLogger(__name__)

class OfflineVLMAnalyzer:
    """Simplified analyzer for slide content extraction"""
    
    def __init__(self):
        self.initialized = False
    
    async def initialize(self):
        """Initialize analyzer (lightweight version)"""
        try:
            logger.info("🤖 Initializing basic VLM analyzer...")
            self.initialized = True
            logger.info("✅ Basic VLM analyzer initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize analyzer: {e}")
            raise
    
    async def analyze_presentation(self, presentation_id: str, file_path: str) -> Dict[str, Any]:
        """Analyze entire presentation and extract content"""
        if not self.initialized:
            await self.initialize()
        
        storage = await get_storage()
        
        try:
            logger.info(f"📋 Starting analysis of presentation {presentation_id}")
            
            # Extract slides from presentation
            slides_data = await self._extract_slides(file_path)
            
            # Analyze each slide
            analyzed_slides = []
            for i, slide_data in enumerate(slides_data):
                logger.info(f"🔍 Analyzing slide {i + 1}/{len(slides_data)}")
                
                analyzed_slide = await self._analyze_slide(slide_data, i)
                analyzed_slides.append(analyzed_slide)
                
                # Update progress
                await storage.update_presentation_status(
                    presentation_id, 
                    f"processing_slide_{i+1}_of_{len(slides_data)}"
                )
            
            # Compile final presentation data
            presentation_data = {
                "presentation_id": presentation_id,
                "total_slides": len(analyzed_slides),
                "slides": analyzed_slides,
                "processing_status": "ready",
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            # Update presentation status
            await storage.update_presentation_status(presentation_id, "ready")
            
            logger.info(f"✅ Analysis complete for presentation {presentation_id}")
            return presentation_data
            
        except Exception as e:
            logger.error(f"❌ Analysis failed: {e}")
            await storage.update_presentation_status(presentation_id, "error")
            raise
    
    async def _extract_slides(self, file_path: str) -> List[Dict[str, Any]]:
        """Extract slides from PDF or PPTX file"""
        file_extension = Path(file_path).suffix.lower()
        
        if file_extension == '.pdf':
            return await self._extract_pdf_slides(file_path)
        elif file_extension in ['.pptx', '.ppt']:
            return await self._extract_pptx_slides(file_path)
        else:
            # Fallback for any file type
            return await self._extract_basic_slide(file_path)
    
    async def _extract_pdf_slides(self, file_path: str) -> List[Dict[str, Any]]:
        """Extract slides from PDF file"""
        slides = []
        
        if not FITZ_AVAILABLE:
            logger.warning("⚠️ PyMuPDF not available, using basic text extraction")
            return await self._extract_basic_slide(file_path)
        
        try:
            # Open PDF document
            pdf_document = fitz.open(file_path)
            
            for page_num in range(pdf_document.page_count):
                page = pdf_document[page_num]
                
                # Extract text
                text_content = page.get_text()
                
                # Create basic slide data without image processing
                slides.append({
                    "slide_number": page_num + 1,
                    "image_data": None,  # Skip image processing for performance
                    "text_content": text_content,
                    "width": 1920,
                    "height": 1080
                })
            
            pdf_document.close()
            logger.info(f"✅ Extracted {len(slides)} slides from PDF")
            return slides
            
        except Exception as e:
            logger.error(f"PDF extraction error: {e}")
            return await self._extract_basic_slide(file_path)
    
    async def _extract_pptx_slides(self, file_path: str) -> List[Dict[str, Any]]:
        """Extract slides from PPTX file"""
        slides = []
        
        if not PPTX_AVAILABLE:
            logger.warning("⚠️ python-pptx not available, using basic extraction")
            return await self._extract_basic_slide(file_path)
        
        try:
            # Open PPTX presentation
            presentation = PPTXPresentation(file_path)
            
            for slide_num, slide in enumerate(presentation.slides):
                # Extract text from shapes
                text_content = ""
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        text_content += shape.text + "\n"
                
                slides.append({
                    "slide_number": slide_num + 1,
                    "image_data": None,  # Skip image processing
                    "text_content": text_content.strip(),
                    "width": 1920,
                    "height": 1080
                })
            
            logger.info(f"✅ Extracted {len(slides)} slides from PPTX")
            return slides
            
        except Exception as e:
            logger.error(f"PPTX extraction error: {e}")
            return await self._extract_basic_slide(file_path)
    
    async def _extract_basic_slide(self, file_path: str) -> List[Dict[str, Any]]:
        """Basic fallback extraction"""
        return [{
            "slide_number": 1,
            "image_data": None,
            "text_content": f"Presentation file uploaded: {Path(file_path).name}\n\nProcessing completed successfully.",
            "width": 1920,
            "height": 1080
        }]
    
    async def _analyze_slide(self, slide_data: Dict[str, Any], slide_index: int) -> Dict[str, Any]:
        """Analyze individual slide"""
        slide_analysis = {
            "slide_id": f"slide_{slide_index + 1}",
            "slide_number": slide_data["slide_number"],
            "image_url": f"slides/slide_{slide_index + 1}.png",
            "semantic_blocks": [],
            "key_points": [],
            "estimated_duration": 60  # Default 1 minute
        }
        
        # Analyze text content
        text_content = slide_data.get("text_content", "")
        if text_content:
            semantic_blocks, key_points = await self._analyze_text_content(text_content)
            slide_analysis["semantic_blocks"] = semantic_blocks
            slide_analysis["key_points"] = key_points
        
        # Estimate duration based on content
        slide_analysis["estimated_duration"] = self._estimate_slide_duration(slide_analysis)
        
        return slide_analysis
    
    async def _analyze_text_content(self, text_content: str) -> tuple:
        """Analyze text content to extract semantic blocks and key points"""
        semantic_blocks = []
        key_points = []
        
        if not text_content.strip():
            return semantic_blocks, key_points
        
        # Split content into lines and analyze structure
        lines = [line.strip() for line in text_content.split('\n') if line.strip()]
        
        for i, line in enumerate(lines):
            # Determine block type based on content
            block_type = self._classify_text_block(line)
            
            # Create semantic block
            semantic_block = {
                "block_id": f"block_{i + 1}",
                "type": block_type,
                "content": line,
                "coordinates": {
                    "x": 50,  # Default positioning
                    "y": 50 + (i * 30),
                    "width": 800,
                    "height": 25
                },
                "importance_score": self._calculate_importance_score(line, block_type)
            }
            
            semantic_blocks.append(semantic_block)
            
            # Extract key points from important text
            if semantic_block["importance_score"] > 0.6:
                key_point = {
                    "point_id": f"key_{i + 1}",
                    "text": line,
                    "keywords": self._extract_keywords(line),
                    "priority": "high" if semantic_block["importance_score"] > 0.8 else "medium",
                    "spoken_status": False
                }
                key_points.append(key_point)
        
        return semantic_blocks, key_points
    
    def _classify_text_block(self, text: str) -> str:
        """Classify text block type based on content"""
        text_lower = text.lower().strip()
        
        # Title indicators
        if len(text) < 60 and (text.isupper() or text.istitle()):
            return "title"
        
        # Bullet point indicators
        if text_lower.startswith(('•', '-', '*', '▪')) or text.startswith(('1.', '2.', '3.')):
            return "bullet_point"
        
        # Chart/figure indicators
        if any(keyword in text_lower for keyword in ['figure', 'chart', 'graph', 'table', 'diagram']):
            return "chart"
        
        # Default to text
        return "text"
    
    def _calculate_importance_score(self, text: str, block_type: str) -> float:
        """Calculate importance score for text block"""
        score = 0.5  # Base score
        
        # Type-based scoring
        type_scores = {
            "title": 0.9,
            "bullet_point": 0.7,
            "chart": 0.8,
            "text": 0.5
        }
        score = type_scores.get(block_type, 0.5)
        
        # Length-based adjustment
        if len(text) > 100:
            score -= 0.1  # Longer text might be less important
        elif len(text) < 20:
            score += 0.1  # Short text might be key point
        
        # Keyword-based boost
        important_keywords = ['key', 'important', 'critical', 'main', 'primary', 'conclusion']
        if any(keyword in text.lower() for keyword in important_keywords):
            score += 0.2
        
        return min(1.0, max(0.0, score))
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text"""
        # Remove punctuation and split
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Filter out common stop words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were'}
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        # Return top 5 keywords
        return keywords[:5]
    
    def _estimate_slide_duration(self, slide_analysis: Dict[str, Any]) -> int:
        """Estimate presentation duration for slide in seconds"""
        base_duration = 30  # Base 30 seconds
        
        # Add time based on content
        text_blocks = len([block for block in slide_analysis["semantic_blocks"] if block["type"] == "text"])
        bullet_points = len([block for block in slide_analysis["semantic_blocks"] if block["type"] == "bullet_point"])
        
        duration = base_duration + (text_blocks * 10) + (bullet_points * 5)
        
        # Cap between 30 seconds and 3 minutes
        return max(30, min(180, duration))

# Global analyzer instance
vlm_analyzer = OfflineVLMAnalyzer()

async def get_vlm_analyzer() -> OfflineVLMAnalyzer:
    """Get VLM analyzer instance"""
    if not vlm_analyzer.initialized:
        await vlm_analyzer.initialize()
    return vlm_analyzer

# Background task for processing presentations
async def process_presentation_async(presentation_id: str, file_path: str):
    """Background task to process presentation"""
    try:
        logger.info(f"🔄 Starting background processing for {presentation_id}")
        analyzer = await get_vlm_analyzer()
        storage = await get_storage()
        
        # Analyze presentation
        result = await analyzer.analyze_presentation(presentation_id, file_path)
        
        # Update presentation data
        await storage.save_presentation(result)
        
        logger.info(f"✅ Background processing complete for {presentation_id}")
        
    except Exception as e:
        logger.error(f"❌ Background processing failed for {presentation_id}: {e}")
        # Update status to error
        try:
            storage = await get_storage()
            await storage.update_presentation_status(presentation_id, "error")
        except:
            pass