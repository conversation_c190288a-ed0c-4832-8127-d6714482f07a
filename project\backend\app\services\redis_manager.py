import redis.asyncio as redis
import json
import logging
from typing import Any, Optional
from app.utils.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)

class RedisManager:
    _redis: Optional[redis.Redis] = None
    
    @property
    def redis(self) -> redis.Redis:
        if self._redis is None:
            raise RuntimeError("Redis not initialized. Call init_redis() first.")
        return self._redis
    
    async def init_connection(self):
        """Initialize Redis connection"""
        try:
            self._redis = redis.from_url(
                settings.REDIS_URL,
                db=settings.REDIS_DB,
                encoding="utf-8",
                decode_responses=True
            )
            
            # Test connection
            await self._redis.ping()
            logger.info("✅ Successfully connected to Redis")
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to Redis: {e}")
            raise
    
    async def close_connection(self):
        """Close Redis connection"""
        if self._redis:
            await self._redis.close()
            logger.info("🔌 Redis connection closed")
    
    # Session management
    async def store_session(self, session_id: str, session_data: dict, expire_time: int = 3600):
        """Store session data with expiration"""
        try:
            await self.redis.setex(
                f"session:{session_id}",
                expire_time,
                json.dumps(session_data, default=str)
            )
            logger.info(f"💾 Session {session_id} stored")
        except Exception as e:
            logger.error(f"❌ Failed to store session: {e}")
            raise
    
    async def get_session(self, session_id: str) -> Optional[dict]:
        """Retrieve session data"""
        try:
            data = await self.redis.get(f"session:{session_id}")
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"❌ Failed to get session: {e}")
            raise
    
    async def update_session(self, session_id: str, updates: dict):
        """Update session data"""
        try:
            current_data = await self.get_session(session_id)
            if current_data:
                current_data.update(updates)
                await self.redis.setex(
                    f"session:{session_id}",
                    3600,  # Reset expiration
                    json.dumps(current_data, default=str)
                )
                logger.info(f"🔄 Session {session_id} updated")
        except Exception as e:
            logger.error(f"❌ Failed to update session: {e}")
            raise
    
    async def delete_session(self, session_id: str):
        """Delete session data"""
        try:
            await self.redis.delete(f"session:{session_id}")
            logger.info(f"🗑️ Session {session_id} deleted")
        except Exception as e:
            logger.error(f"❌ Failed to delete session: {e}")
            raise
    
    # Real-time data caching
    async def cache_presentation(self, presentation_id: str, presentation_data: dict):
        """Cache presentation data for quick access"""
        try:
            await self.redis.setex(
                f"presentation:{presentation_id}",
                1800,  # 30 minutes
                json.dumps(presentation_data, default=str)
            )
            logger.info(f"📋 Presentation {presentation_id} cached")
        except Exception as e:
            logger.error(f"❌ Failed to cache presentation: {e}")
            raise
    
    async def get_cached_presentation(self, presentation_id: str) -> Optional[dict]:
        """Get cached presentation data"""
        try:
            data = await self.redis.get(f"presentation:{presentation_id}")
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"❌ Failed to get cached presentation: {e}")
            raise
    
    # Real-time events
    async def publish_event(self, channel: str, event_data: dict):
        """Publish real-time event"""
        try:
            await self.redis.publish(
                channel,
                json.dumps(event_data, default=str)
            )
            logger.info(f"📡 Event published to {channel}")
        except Exception as e:
            logger.error(f"❌ Failed to publish event: {e}")
            raise
    
    async def subscribe_to_channel(self, channel: str):
        """Subscribe to a channel for real-time events"""
        try:
            pubsub = self.redis.pubsub()
            await pubsub.subscribe(channel)
            logger.info(f"📻 Subscribed to {channel}")
            return pubsub
        except Exception as e:
            logger.error(f"❌ Failed to subscribe to channel: {e}")
            raise
    
    # Performance metrics
    async def increment_metric(self, metric_name: str, value: int = 1):
        """Increment a performance metric"""
        try:
            await self.redis.incrby(f"metric:{metric_name}", value)
        except Exception as e:
            logger.error(f"❌ Failed to increment metric: {e}")
    
    async def get_metric(self, metric_name: str) -> int:
        """Get performance metric value"""
        try:
            value = await self.redis.get(f"metric:{metric_name}")
            return int(value) if value else 0
        except Exception as e:
            logger.error(f"❌ Failed to get metric: {e}")
            return 0
    
    # Rate limiting
    async def check_rate_limit(self, key: str, limit: int, window: int) -> bool:
        """Check if action is within rate limit"""
        try:
            current = await self.redis.get(f"rate_limit:{key}")
            if current is None:
                await self.redis.setex(f"rate_limit:{key}", window, 1)
                return True
            elif int(current) < limit:
                await self.redis.incr(f"rate_limit:{key}")
                return True
            else:
                return False
        except Exception as e:
            logger.error(f"❌ Failed to check rate limit: {e}")
            return False

# Global Redis manager instance
redis_manager = RedisManager()

async def init_redis():
    """Initialize Redis connection"""
    await redis_manager.init_connection()

async def get_redis() -> RedisManager:
    """Get Redis manager instance"""
    return redis_manager