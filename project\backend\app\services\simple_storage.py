"""
Simplified Local Storage Manager for AI-Powered Presentation System
"""

import json
import asyncio
import aiofiles
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class SimpleStorage:
    """Simplified local storage manager without complex configuration"""
    
    def __init__(self, base_dir: str = "data"):
        self.base_dir = Path(base_dir)
        self.presentations_dir = self.base_dir / "presentations"
        self.analysis_dir = self.base_dir / "analysis"
        self.sessions_dir = self.base_dir / "sessions"
        
    async def initialize(self):
        """Initialize storage directories"""
        try:
            # Create directories
            for directory in [self.presentations_dir, self.analysis_dir, self.sessions_dir]:
                directory.mkdir(parents=True, exist_ok=True)
            
            logger.info("✅ Simple storage initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize simple storage: {e}")
            raise
    
    # Presentation operations
    async def save_presentation(self, presentation_id: str, data: Dict[str, Any]) -> None:
        """Save presentation data to JSON file"""
        file_path = self.presentations_dir / f"{presentation_id}.json"
        
        async with aiofiles.open(file_path, 'w') as f:
            await f.write(json.dumps(data, default=str, indent=2))
        
        logger.info(f"💾 Presentation {presentation_id} saved")
    
    async def get_presentation(self, presentation_id: str) -> Optional[Dict[str, Any]]:
        """Get presentation data from JSON file"""
        file_path = self.presentations_dir / f"{presentation_id}.json"
        
        if not file_path.exists():
            return None
        
        try:
            async with aiofiles.open(file_path, 'r') as f:
                content = await f.read()
                return json.loads(content)
        except Exception as e:
            logger.error(f"Error loading presentation {presentation_id}: {e}")
            return None
    
    async def list_presentations(self) -> List[Dict[str, Any]]:
        """List all presentations"""
        presentations = []
        
        if not self.presentations_dir.exists():
            return presentations
        
        for file_path in self.presentations_dir.glob("*.json"):
            try:
                async with aiofiles.open(file_path, 'r') as f:
                    content = await f.read()
                    data = json.loads(content)
                    presentations.append({
                        'presentation_id': data.get('presentation_id'),
                        'title': data.get('title'),
                        'status': data.get('status'),
                        'upload_timestamp': data.get('upload_timestamp')
                    })
            except Exception as e:
                logger.error(f"Error loading presentation from {file_path}: {e}")
        
        # Sort by upload timestamp (newest first)
        presentations.sort(key=lambda x: x.get('upload_timestamp', ''), reverse=True)
        return presentations
    
    # Analysis operations
    async def save_analysis(self, presentation_id: str, analysis_data: Dict[str, Any]) -> None:
        """Save AI analysis data"""
        file_path = self.analysis_dir / f"{presentation_id}_analysis.json"
        
        async with aiofiles.open(file_path, 'w') as f:
            await f.write(json.dumps(analysis_data, default=str, indent=2))
        
        logger.info(f"📊 Analysis for {presentation_id} saved")
    
    async def get_analysis(self, presentation_id: str) -> Optional[Dict[str, Any]]:
        """Get AI analysis data"""
        file_path = self.analysis_dir / f"{presentation_id}_analysis.json"
        
        if not file_path.exists():
            return None
        
        try:
            async with aiofiles.open(file_path, 'r') as f:
                content = await f.read()
                return json.loads(content)
        except Exception as e:
            logger.error(f"Error loading analysis for {presentation_id}: {e}")
            return None
    
    # Session operations
    async def save_session(self, session_id: str, session_data: Dict[str, Any]) -> None:
        """Save session data"""
        file_path = self.sessions_dir / f"{session_id}.json"
        
        async with aiofiles.open(file_path, 'w') as f:
            await f.write(json.dumps(session_data, default=str, indent=2))
        
        logger.info(f"🎭 Session {session_id} saved")
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data"""
        file_path = self.sessions_dir / f"{session_id}.json"
        
        if not file_path.exists():
            return None
        
        try:
            async with aiofiles.open(file_path, 'r') as f:
                content = await f.read()
                return json.loads(content)
        except Exception as e:
            logger.error(f"Error loading session {session_id}: {e}")
            return None