"""
Real-time Voice Processing Service
Handles keyword spotting, speech-to-text, and content tracking for presentations
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from datetime import datetime
import re
import difflib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class KeyPoint:
    """Represents a key talking point from a slide"""
    id: str
    text: str
    keywords: List[str]
    slide_id: str
    is_completed: bool = False
    confidence: float = 0.0
    detected_at: Optional[datetime] = None
    detection_count: int = 0

@dataclass
class VoiceEvent:
    """Represents a voice detection event"""
    transcript: str
    confidence: float
    timestamp: datetime
    detected_keywords: List[str]
    matched_key_points: List[str]

class KeywordSpotter:
    """Advanced keyword spotting engine with fuzzy matching"""
    
    def __init__(self):
        self.stop_words = {
            'the', 'is', 'at', 'which', 'on', 'and', 'a', 'an', 'as', 'are', 
            'was', 'were', 'been', 'be', 'have', 'has', 'had', 'do', 'does', 
            'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must',
            'can', 'to', 'of', 'in', 'for', 'with', 'by', 'from', 'up', 'about',
            'into', 'through', 'during', 'before', 'after', 'above', 'below',
            'up', 'down', 'out', 'off', 'over', 'under', 'again', 'further',
            'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how',
            'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other',
            'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so',
            'than', 'too', 'very', 'just', 'now'
        }
    
    def extract_keywords(self, text: str, min_length: int = 3) -> List[str]:
        """Extract meaningful keywords from text"""
        # Clean and tokenize
        words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        
        # Filter out stop words and short words
        keywords = [
            word for word in words 
            if word not in self.stop_words and len(word) >= min_length
        ]
        
        return list(set(keywords))  # Remove duplicates
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate semantic similarity between two texts"""
        keywords1 = set(self.extract_keywords(text1))
        keywords2 = set(self.extract_keywords(text2))
        
        if not keywords1 or not keywords2:
            return 0.0
        
        # Jaccard similarity
        intersection = len(keywords1.intersection(keywords2))
        union = len(keywords1.union(keywords2))
        
        jaccard = intersection / union if union > 0 else 0.0
        
        # Also check for fuzzy string matching
        sequence_similarity = difflib.SequenceMatcher(
            None, text1.lower(), text2.lower()
        ).ratio()
        
        # Weighted combination
        return 0.7 * jaccard + 0.3 * sequence_similarity
    
    def find_keyword_matches(self, transcript: str, keywords: List[str]) -> List[str]:
        """Find which keywords are mentioned in the transcript"""
        transcript_lower = transcript.lower()
        transcript_keywords = set(self.extract_keywords(transcript))
        
        matches = []
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            
            # Direct substring match
            if keyword_lower in transcript_lower:
                matches.append(keyword)
                continue
            
            # Fuzzy matching for individual words
            for trans_keyword in transcript_keywords:
                if difflib.SequenceMatcher(
                    None, keyword_lower, trans_keyword
                ).ratio() > 0.8:
                    matches.append(keyword)
                    break
        
        return matches

class VoiceProcessor:
    """Main voice processing service"""
    
    def __init__(self):
        self.keyword_spotter = KeywordSpotter()
        self.active_sessions: Dict[str, Dict] = {}
        self.voice_events: Dict[str, List[VoiceEvent]] = {}
        
    async def initialize_session(self, session_id: str, key_points: List[Dict]) -> bool:
        """Initialize a voice monitoring session"""
        try:
            processed_key_points = []
            
            for kp_data in key_points:
                keywords = self.keyword_spotter.extract_keywords(kp_data['text'])
                key_point = KeyPoint(
                    id=kp_data['id'],
                    text=kp_data['text'],
                    keywords=keywords,
                    slide_id=kp_data.get('slide_id', ''),
                )
                processed_key_points.append(key_point)
            
            self.active_sessions[session_id] = {
                'key_points': {kp.id: kp for kp in processed_key_points},
                'current_slide': None,
                'start_time': datetime.now(),
                'total_detections': 0,
                'confidence_threshold': 0.6
            }
            
            self.voice_events[session_id] = []
            
            logger.info(f"Initialized voice session {session_id} with {len(processed_key_points)} key points")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize session {session_id}: {e}")
            return False
    
    async def process_voice_input(
        self, 
        session_id: str, 
        transcript: str, 
        confidence: float = 1.0
    ) -> Dict:
        """Process incoming voice transcript and detect key points"""
        
        if session_id not in self.active_sessions:
            return {"error": "Session not found"}
        
        session = self.active_sessions[session_id]
        key_points = session['key_points']
        threshold = session['confidence_threshold']
        
        # Create voice event
        voice_event = VoiceEvent(
            transcript=transcript,
            confidence=confidence,
            timestamp=datetime.now(),
            detected_keywords=[],
            matched_key_points=[]
        )
        
        detected_key_points = []
        
        # Check transcript against all key points
        for kp_id, key_point in key_points.items():
            if key_point.is_completed:
                continue
            
            # Calculate similarity score
            similarity = self.keyword_spotter.calculate_similarity(
                transcript, key_point.text
            )
            
            # Find specific keyword matches
            keyword_matches = self.keyword_spotter.find_keyword_matches(
                transcript, key_point.keywords
            )
            
            # Boost similarity if keywords are found
            if keyword_matches:
                keyword_boost = len(keyword_matches) / len(key_point.keywords) * 0.3
                similarity = min(1.0, similarity + keyword_boost)
            
            # Check if detection threshold is met
            if similarity >= threshold:
                key_point.confidence = similarity
                key_point.detection_count += 1
                
                # Mark as completed if confidence is high enough
                if similarity >= 0.8 or key_point.detection_count >= 2:
                    key_point.is_completed = True
                    key_point.detected_at = datetime.now()
                
                detected_key_points.append({
                    'id': kp_id,
                    'text': key_point.text,
                    'confidence': similarity,
                    'is_completed': key_point.is_completed,
                    'keyword_matches': keyword_matches
                })
                
                voice_event.matched_key_points.append(kp_id)
                voice_event.detected_keywords.extend(keyword_matches)
        
        # Store voice event
        self.voice_events[session_id].append(voice_event)
        session['total_detections'] += len(detected_key_points)
        
        # Prepare response
        response = {
            'session_id': session_id,
            'transcript': transcript,
            'detected_key_points': detected_key_points,
            'session_stats': {
                'total_key_points': len(key_points),
                'completed_key_points': sum(1 for kp in key_points.values() if kp.is_completed),
                'total_detections': session['total_detections'],
                'session_duration': (datetime.now() - session['start_time']).total_seconds()
            }
        }
        
        logger.info(f"Processed voice input for session {session_id}: {len(detected_key_points)} detections")
        return response
    
    async def get_session_status(self, session_id: str) -> Dict:
        """Get current session status and statistics"""
        if session_id not in self.active_sessions:
            return {"error": "Session not found"}
        
        session = self.active_sessions[session_id]
        key_points = session['key_points']
        
        return {
            'session_id': session_id,
            'is_active': True,
            'key_points': [
                {
                    'id': kp.id,
                    'text': kp.text,
                    'keywords': kp.keywords,
                    'is_completed': kp.is_completed,
                    'confidence': kp.confidence,
                    'detected_at': kp.detected_at.isoformat() if kp.detected_at else None,
                    'detection_count': kp.detection_count
                }
                for kp in key_points.values()
            ],
            'stats': {
                'total_key_points': len(key_points),
                'completed_key_points': sum(1 for kp in key_points.values() if kp.is_completed),
                'completion_rate': sum(1 for kp in key_points.values() if kp.is_completed) / len(key_points) if key_points else 0,
                'total_detections': session['total_detections'],
                'session_duration': (datetime.now() - session['start_time']).total_seconds(),
                'average_confidence': sum(kp.confidence for kp in key_points.values()) / len(key_points) if key_points else 0
            }
        }
    
    async def update_current_slide(self, session_id: str, slide_id: str) -> bool:
        """Update the current slide for more context-aware processing"""
        if session_id not in self.active_sessions:
            return False
        
        self.active_sessions[session_id]['current_slide'] = slide_id
        logger.info(f"Updated current slide for session {session_id}: {slide_id}")
        return True
    
    async def adjust_sensitivity(self, session_id: str, threshold: float) -> bool:
        """Adjust the confidence threshold for detection"""
        if session_id not in self.active_sessions:
            return False
        
        threshold = max(0.1, min(1.0, threshold))  # Clamp between 0.1 and 1.0
        self.active_sessions[session_id]['confidence_threshold'] = threshold
        logger.info(f"Adjusted confidence threshold for session {session_id}: {threshold}")
        return True
    
    async def get_voice_events(self, session_id: str, limit: int = 50) -> List[Dict]:
        """Get recent voice events for debugging/analysis"""
        if session_id not in self.voice_events:
            return []
        
        events = self.voice_events[session_id][-limit:]
        return [
            {
                'transcript': event.transcript,
                'confidence': event.confidence,
                'timestamp': event.timestamp.isoformat(),
                'detected_keywords': event.detected_keywords,
                'matched_key_points': event.matched_key_points
            }
            for event in events
        ]
    
    async def cleanup_session(self, session_id: str) -> bool:
        """Clean up session data"""
        try:
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            if session_id in self.voice_events:
                del self.voice_events[session_id]
            
            logger.info(f"Cleaned up voice session {session_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to cleanup session {session_id}: {e}")
            return False

# Global voice processor instance
voice_processor = VoiceProcessor()

# Convenience functions for FastAPI integration
async def initialize_voice_session(session_id: str, key_points: List[Dict]) -> bool:
    """Initialize voice monitoring for a presentation session"""
    return await voice_processor.initialize_session(session_id, key_points)

async def process_speech_input(session_id: str, transcript: str, confidence: float = 1.0) -> Dict:
    """Process speech input and return detection results"""
    return await voice_processor.process_voice_input(session_id, transcript, confidence)

async def get_voice_session_status(session_id: str) -> Dict:
    """Get voice session status and progress"""
    return await voice_processor.get_session_status(session_id)

async def cleanup_voice_session(session_id: str) -> bool:
    """Clean up voice session resources"""
    return await voice_processor.cleanup_session(session_id)