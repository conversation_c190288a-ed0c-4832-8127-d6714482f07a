{"analysis": {"presentation_id": "8ee1227b-5858-4154-8c87-e16b885de1c5", "title": "8ee1227b-5858-4154-8c87-e16b885de1c5_samplepptx", "total_slides": 2, "slides": [{"slide_number": 1, "title": "Sample PowerPoint File", "text_content": "Sample PowerPoint File\nSt. Cloud Technical College", "key_points": ["Overview of St. Cloud technical college's engineering program offerings", "Detailed curriculum map for associate and bachelor degrees in Engineering Technology, including core subjects such as mathematics, physics, and computer science; specialized areas like CAD/CAM or electrical systems; practical workshop experience requirements", "Information on the faculty qualifications with their specific expertise aligned to industry needs", "Industry partnerships for internships and job placements showcasing successful graduate employment rates in various engineering sectors, including local businesses like Honeywell Aerospace or medical technology firms such as Medtronic", "Campus facilities highlighting modern labs with industry standard equipment relevant to the technical program areas discussed; include green energy initiatives on campus demonstrating commitment to sustainable practices in education and operation"], "semantic_blocks": [{"type": "title", "content": "Sample PowerPoint File", "coordinates": {"x": 50, "y": 50, "width": 700, "height": 100}}, {"type": "content", "content": "Sample PowerPoint File\nSt. Cloud Technical College", "coordinates": {"x": 50, "y": 200, "width": 700, "height": 400}}], "complexity_score": 0.46, "estimated_duration": 30}, {"slide_number": 2, "title": "This is a Sample Slide", "text_content": "This is a Sample Slide\nHere is an outline of bulleted points\nYou can print out PPT files as handouts using the \u000bPRINT > \u000b  PRINT WHAT > HANDOUTS option", "key_points": ["Presentation'in content and delivery.", "Utilize visual aids to enhance message retention.", "Practice speaking clearly and confidently for audience engagement.", "Craft an attention-grabbing opening statement or hook.", "Organize slides logically, with clear headings and concise points per slide."], "semantic_blocks": [{"type": "title", "content": "This is a Sample Slide", "coordinates": {"x": 50, "y": 50, "width": 700, "height": 100}}, {"type": "content", "content": "This is a Sample Slide\nHere is an outline of bulleted points\nYou can print out PPT files as handouts using the \u000bPRINT > \u000b  PRINT WHAT > HANDOUTS option", "coordinates": {"x": 50, "y": 200, "width": 700, "height": 400}}], "complexity_score": 1.0, "estimated_duration": 116}], "overall_themes": [". Overview and Introduction to St. Cloud Technical College's programs", ". Importance of education in personal growth and employability", ". Highlighting specific courses, degrees offered (e.g., Automotive Technology & Diesel Service)", ". Career prospects for graduates with technical skills", ". Encouragement to enroll and pursue further studies at St. Cloud Technical College"], "difficulty_level": "Advanced", "estimated_total_duration": 146, "key_concepts": [". Overview and Introduction to St. Cloud Technical College's programs", ". Importance of education in personal growth and employability", ". Highlighting specific courses, degrees offered (e.g., Automotive Technology & Diesel Service)"], "analysis_timestamp": "2025-09-07T14:58:37.085792"}, "insights": {"overview": {"total_slides": 2, "estimated_duration_minutes": 2.4, "difficulty_level": "Advanced", "main_themes": [". Overview and Introduction to St. Cloud Technical College's programs", ". Importance of education in personal growth and employability", ". Highlighting specific courses, degrees offered (e.g., Automotive Technology & Diesel Service)", ". Career prospects for graduates with technical skills", ". Encouragement to enroll and pursue further studies at St. Cloud Technical College"]}, "slide_breakdown": [{"slide_number": 1, "title": "Sample PowerPoint File", "key_points_count": 5, "complexity_score": 0.46, "estimated_duration_seconds": 30}, {"slide_number": 2, "title": "This is a Sample Slide", "key_points_count": 5, "complexity_score": 1.0, "estimated_duration_seconds": 116}], "recommendations": ["Consider simplifying complex slides for better audience comprehension"], "key_statistics": {"average_complexity": 0.73, "total_key_points": 10, "slides_with_images": 0, "longest_slide_duration": 116, "shortest_slide_duration": 30}}}