{"analysis": {"presentation_id": "ace10485-503e-41ce-97c5-8e920d97c28b", "title": "ace10485-503e-41ce-97c5-8e920d97c28b_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_CV (6)", "total_slides": 2, "slides": [{"slide_number": 1, "title": "<PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON> Aham<PERSON>i House, Farook College PO, Kozhikode, Kerala, India, 673632\n+91 **********\n—\n<EMAIL>\n—\nlinkedin.com/in/ziyad-ahammed\n—\nziyad-portfolio.web.app\n—\ngithub.com/Ziyad765\nPROFESSIONAL SUMMARY\nInnovative Computer Science and Design Engineering student with hands-on experience in AI, robotics, and\nfull-stack development. Proven ability to lead and deliver impactful projects, demonstrated through a continuing\nrobotics R&D internship, leadership as IEEE Computer Society Chairperson, and multiple hackathon awards.\nEager to apply skills in machine learning and user-centric design to solve complex real-world challenges.\nEDUCATION\nB.Tech in Computer Science and Design Engineering\nNov 2022 – Aug 2026\nGovernment Engineering College, Kozhikode — KTU (APJ Abdul Kalam Technological University)\n• Current CGPA: 7.1/10\n• Key Courses: UI/UX Design, Game Development, Artificial Intelligence, Machine Learning\nTECHNICAL SKILLS\nProgramming\nC, Python, HTML/CSS, JavaScript, C++, Node.js\nAI/ML\nOpenCV, TensorFlow, Speech Recognition, NLP, Pandas, NumPy\nWeb Development\nReact.js, Flask, Firebase, MongoDB\nGame Development\nUnity, C#\nHardware & IoT\nRaspberry Pi, ESP32, Arduino\nUI/UX & Design\nFigma, Tailwind CSS\nTools\nGit, GitHub, VS Code, Jupyter Notebook\nPROJECTS\nSmart Commerce Platform – PiHack 2.0 Special Mention\nAI/ML, XGBoost, Python\n• Won special mention for an AI platform using XGBoost to forecast demand and cut inventory mismanagement\nby 25%.\nENIAC: Conversational AI Robot\nRaspberry Pi, Python, NLP\n• Engineered a conversational AI robot on Raspberry Pi with integrated NLP and speech recognition for inter-\naction.\nPostCare: Postpartum Depression Support App\nNode.js, Socket.io, HTML/CSS\n• Achieved finalist status at WIEHACK 4.0 for a Node.js app providing real-time mental health support.\nMoodBot: AI-Powered Emotional Chatbot\nOpenCV, Python, Tkinter\n• Built a top-ranked chatbot using OpenCV for real-time facial emotion recognition and response.\nCyber Safety Awareness Game\nUnity, C#\n• Placed as a top 6 finalist for a 3D Unity game designed to educate non-technical users on cyber safety.\n", "text_content": "<PERSON><PERSON><PERSON> Aham<PERSON>i House, Farook College PO, Kozhikode, Kerala, India, 673632\n+91 **********\n—\n<EMAIL>\n—\nlinkedin.com/in/ziyad-ahammed\n—\nziyad-portfolio.web.app\n—\ngithub.com/Ziyad765\nPROFESSIONAL SUMMARY\nInnovative Computer Science and Design Engineering student with hands-on experience in AI, robotics, and\nfull-stack development. Proven ability to lead and deliver impactful projects, demonstrated through a continuing\nrobotics R&D internship, leadership as IEEE Computer Society Chairperson, and multiple hackathon awards.\nEager to apply skills in machine learning and user-centric design to solve complex real-world challenges.\nEDUCATION\nB.Tech in Computer Science and Design Engineering\nNov 2022 – Aug 2026\nGovernment Engineering College, Kozhikode — KTU (APJ Abdul Kalam Technological University)\n• Current CGPA: 7.1/10\n• Key Courses: UI/UX Design, Game Development, Artificial Intelligence, Machine Learning\nTECHNICAL SKILLS\nProgramming\nC, Python, HTML/CSS, JavaScript, C++, Node.js\nAI/ML\nOpenCV, TensorFlow, Speech Recognition, NLP, Pandas, NumPy\nWeb Development\nReact.js, Flask, Firebase, MongoDB\nGame Development\nUnity, C#\nHardware & IoT\nRaspberry Pi, ESP32, Arduino\nUI/UX & Design\nFigma, Tailwind CSS\nTools\nGit, GitHub, VS Code, Jupyter Notebook\nPROJECTS\nSmart Commerce Platform – PiHack 2.0 Special Mention\nAI/ML, XGBoost, Python\n• Won special mention for an AI platform using XGBoost to forecast demand and cut inventory mismanagement\nby 25%.\nENIAC: Conversational AI Robot\nRaspberry Pi, Python, NLP\n• Engineered a conversational AI robot on Raspberry Pi with integrated NLP and speech recognition for inter-\naction.\nPostCare: Postpartum Depression Support App\nNode.js, Socket.io, HTML/CSS\n• Achieved finalist status at WIEHACK 4.0 for a Node.js app providing real-time mental health support.\nMoodBot: AI-Powered Emotional Chatbot\nOpenCV, Python, Tkinter\n• Built a top-ranked chatbot using OpenCV for real-time facial emotion recognition and response.\nCyber Safety Awareness Game\nUnity, C#\n• Placed as a top 6 finalist for a 3D Unity game designed to educate non-technical users on cyber safety.\n", "key_points": ["Innovative computer science and design engineering student with hands-on experience in AI, robotics, full-stack development; proven ability as IEEE Chairperson for continuing projects leading to real-world impacts through a robotics R&D internship. Eager application of machine learning and user-centric design skills to complex challenges; CGPA 7.1/10 with key courses in UI/UX, Game Development, AI, ML; programming languages proficiency (C, Python among others); extensive technical skill set including OpenCV for computer vision tasks, TensorFlow and Machine Learning models like XGBoost; leadership experience as IEEE Computer Society Chairperson. Key projects include Smart Commerce Platform PiHack 2.0 with AI demand forecasting using XGBoost to reduce inventory mismanagement by 25%, conversational AI robot Eniac for post-interaction, PostCare mental health support app as a WIEHACK finalist in Node.js and Socket.io framework; MoodBot chatbot employing OpenCV for real-time facial emotion recognition with <PERSON><PERSON><PERSON> interface; Cyber Safety Awareness Game using Unity 3D to educate users on cyber safety principles through interactive gameplay experience as top finalist in WIEHACK."], "semantic_blocks": [{"type": "title", "content": "<PERSON><PERSON><PERSON>", "coordinates": {"x": 50, "y": 50, "width": 700, "height": 100}}, {"type": "content", "content": "<PERSON><PERSON><PERSON>, Farook College PO, Kozhikode, Kerala, India, 673632\n+91 **********\n—\n<EMAIL>\n—\nlinkedin.com/in/ziyad-ahammed\n—\nziyad-portfolio.web.app\n—\ngithub.com/...", "coordinates": {"x": 50, "y": 200, "width": 700, "height": 400}}], "complexity_score": 1.0, "difficulty": "hard", "estimated_duration": 300}, {"slide_number": 2, "title": "INTERNSHIP EXPERIENCE", "content": "INTERNSHIP EXPERIENCE\nSTEM-Xpert (Robotic Research and Development)\nRobotics Development Intern (Continuing)\n• Contribute to R&D projects, gaining hands-on experience in cutting-edge robotics technologies.\nSTEM-Xpert, Techgenius Robotics and Technology Ventures LLP Robotics Development Intern (3 months)\n• Learned professional IT workflows, including CRM systems and structured reporting protocols.\nMaiTexa Technologies Pvt Ltd\nPython & Machine Learning Intern (1 month)\n• Applied ML techniques and developed Python-based solutions to address real-world business challenges.\nLEADERSHIP EXPERIENCE\nIEEE Computer Society, GEC Kozhikode\nChairperson\n• Lead a student organization, organizing technical workshops and events to advance CS knowledge.\nAKCSSC 2024 (IEEE All Kerala Computer Society Student Convention)\nStudent Technical Lead\n• Led technical planning and execution for a state-level IEEE student convention.\nLakshya ’24 Techfest\nDepartment Head of CS & Design\n• Managed and coordinated all departmental technical events and competitions for the college’s annual techfest.\nEndora 2.0 2025 Techfest\nTechnical Lead\n• Guided technical implementation and mentored team members for a major college techfest.\nGovernment Engineering College Kozhikode Class Representative , Students Placement Representative of\nCSE Department\n• Serve as the primary liaison between students and faculty to address academic and Placement concerns.\nACHIEVEMENTS & RECOGNITIONS\n• Special Mention Award: PiHack 2.0 (2025) – AI-powered commerce platform.\n• Top 6 Finalist: Innovate Hackathon 2025 – Interactive cybersecurity game.\n• Top Project: TinkerHub Useless Projects Make-a-thon – AI emotional support chatbot.\n• Finalist: WIEHACK 4.0 – PostCare mental health support application.\n• Top 20 Finalist: IEEE ”Hack the Metaverse” (IIIT Bangalore, 2023).\nCERTIFICATIONS\n• Robotics Development Certification – STEM-Xpert, Techgenius Robotics and Technology Ventures LLP\n• Bootcamp on Artificial Intelligence – NIELIT Calicut\n• Ethical Hacking & Cyber Security Training – RedTeam Calicut\n", "text_content": "INTERNSHIP EXPERIENCE\nSTEM-Xpert (Robotic Research and Development)\nRobotics Development Intern (Continuing)\n• Contribute to R&D projects, gaining hands-on experience in cutting-edge robotics technologies.\nSTEM-Xpert, Techgenius Robotics and Technology Ventures LLP Robotics Development Intern (3 months)\n• Learned professional IT workflows, including CRM systems and structured reporting protocols.\nMaiTexa Technologies Pvt Ltd\nPython & Machine Learning Intern (1 month)\n• Applied ML techniques and developed Python-based solutions to address real-world business challenges.\nLEADERSHIP EXPERIENCE\nIEEE Computer Society, GEC Kozhikode\nChairperson\n• Lead a student organization, organizing technical workshops and events to advance CS knowledge.\nAKCSSC 2024 (IEEE All Kerala Computer Society Student Convention)\nStudent Technical Lead\n• Led technical planning and execution for a state-level IEEE student convention.\nLakshya ’24 Techfest\nDepartment Head of CS & Design\n• Managed and coordinated all departmental technical events and competitions for the college’s annual techfest.\nEndora 2.0 2025 Techfest\nTechnical Lead\n• Guided technical implementation and mentored team members for a major college techfest.\nGovernment Engineering College Kozhikode Class Representative , Students Placement Representative of\nCSE Department\n• Serve as the primary liaison between students and faculty to address academic and Placement concerns.\nACHIEVEMENTS & RECOGNITIONS\n• Special Mention Award: PiHack 2.0 (2025) – AI-powered commerce platform.\n• Top 6 Finalist: Innovate Hackathon 2025 – Interactive cybersecurity game.\n• Top Project: TinkerHub Useless Projects Make-a-thon – AI emotional support chatbot.\n• Finalist: WIEHACK 4.0 – PostCare mental health support application.\n• Top 20 Finalist: IEEE ”Hack the Metaverse” (IIIT Bangalore, 2023).\nCERTIFICATIONS\n• Robotics Development Certification – STEM-Xpert, Techgenius Robotics and Technology Ventures LLP\n• Bootcamp on Artificial Intelligence – NIELIT Calicut\n• Ethical Hacking & Cyber Security Training – RedTeam Calicut\n", "key_points": ["Contribute to R&D projects in robotics technologies at STEM-Xpert.", "Learned professional IT workflows, including CRM systems and reporting protocols at Techgenius Robotics & Technology Ventures LLP.", "Apply ML techniques for real-world business challenges during internship with MaiTexa Technologies Pvt Ltd in Python and Machine Learning.", "Lead a student organization as the Chairperson of IEEE Computer Society, GEC Kozhikode to advance computer science knowledge through technical workshops and events.", "Manage planning for state-level IEEE student convention at AKCSSC 2024 (IEEE All Kerala Computer Society Student Convention)."], "semantic_blocks": [{"type": "title", "content": "INTERNSHIP EXPERIENCE", "coordinates": {"x": 50, "y": 50, "width": 700, "height": 100}}, {"type": "content", "content": "INTERNSHIP EXPERIENCE\nSTEM-Xpert (Robotic Research and Development)\nRobotics Development Intern (Continuing)\n• Contribute to R&D projects, gaining hands-on experience in cutting-edge robotics technolo...", "coordinates": {"x": 50, "y": 200, "width": 700, "height": 400}}], "complexity_score": 1.0, "difficulty": "hard", "estimated_duration": 300}], "overall_themes": [". AI/ML Projects – Emphasizing <PERSON><PERSON><PERSON>'s work on the Smart Commerce Platform, ENIAC robotic project for inter-action using NLP, a mental health support app targeting postpartum depression awareness and assistance (PostCare), and MoodBot which integrates emotion recognition with chatbot technology.", ". Robotics & AI Integration – Focus on <PERSON><PERSON><PERSON>'s development of the ENIAC project, a conversational AI robot using Raspberry Pi that employs natural language processing (NLP) and speech recognition for interactive communication capabilities in healthcare settings such as postpartum care.", ". Full-Stack Development & Web Technologies – Highlighting Ziyad's comprehensive skill set across various technologies, including proficiency with frontend tools like Figma, Tailwind CSS; backend frameworks and languages such as React."], "difficulty_level": "advanced", "estimated_total_duration": 600, "key_concepts": [". AI/ML Projects – Emphasizing <PERSON><PERSON><PERSON>'s work on the Smart Commerce Platform, ENIAC robotic project for inter-action using NLP, a mental health support app targeting postpartum depression awareness and assistance (PostCare), and MoodBot which integrates emotion recognition with chatbot technology.", ". Robotics & AI Integration – Focus on <PERSON><PERSON><PERSON>'s development of the ENIAC project, a conversational AI robot using Raspberry Pi that employs natural language processing (NLP) and speech recognition for interactive communication capabilities in healthcare settings such as postpartum care.", ". Full-Stack Development & Web Technologies – Highlighting Ziyad's comprehensive skill set across various technologies, including proficiency with frontend tools like Figma, Tailwind CSS; backend frameworks and languages such as React."], "analysis_timestamp": "2025-09-22T14:15:24.907910"}, "insights": {"summary": "This advanced level presentation contains 2 slides covering . AI/ML Projects – Emphasizing <PERSON><PERSON><PERSON>'s work on the Smart Commerce Platform, ENIAC robotic project for inter-action using NLP, a mental health support app targeting postpartum depression awareness and assistance (PostCare), and MoodBot which integrates emotion recognition with chatbot technology., . Robotics & AI Integration – Focus on <PERSON><PERSON><PERSON>'s development of the ENIAC project, a conversational AI robot using Raspberry Pi that employs natural language processing (NLP) and speech recognition for interactive communication capabilities in healthcare settings such as postpartum care., . Full-Stack Development & Web Technologies – Highlighting Z<PERSON><PERSON>'s comprehensive skill set across various technologies, including proficiency with frontend tools like Figma, Tailwind CSS; backend frameworks and languages such as React.. Estimated duration: 10 minutes.", "recommendations": ["Consider simplifying complex slides for better audience comprehension"], "strengths": ["Well-structured content with 3 main themes", "Appropriate advanced difficulty level", "Good balance with 6 total key points"], "areas_for_improvement": ["Consider adding more visual elements", "Consistent complexity throughout"]}}