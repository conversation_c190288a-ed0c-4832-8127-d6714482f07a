import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Mi<PERSON>, Mic<PERSON><PERSON>, Volume2, AlertCircle } from 'lucide-react';
import { useSettings } from '../contexts/SettingsContext';

interface EnhancedVoiceMonitorProps {
  isActive: boolean;
  onVoiceCommand?: (command: string) => void;
  onKeyPointDetected?: (keyPoint: string, confidence: number) => void;
  onTranscript?: (text: string) => void;
  keyPoints?: string[];
  sensitivity?: number;
}

const EnhancedVoiceMonitor: React.FC<EnhancedVoiceMonitorProps> = ({
  isActive,
  onVoiceCommand,
  onKeyPointDetected,
  onTranscript,
  keyPoints = [],
  sensitivity
}) => {
  const { settings } = useSettings();

  // Use settings sensitivity if not provided as prop
  const effectiveSensitivity = sensitivity ?? settings.keywordDetectionThreshold;
  const [isListening, setIsListening] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);

  const recognitionRef = useRef<any>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationRef = useRef<number>();

  // Fuzzy string matching with improved algorithm
  const fuzzyMatch = useCallback((str1: string, str2: string): number => {
    const s1 = str1.toLowerCase().trim();
    const s2 = str2.toLowerCase().trim();

    if (s1 === s2) return 1.0;
    if (s1.length === 0 || s2.length === 0) return 0;

    // Check for substring matches
    if (s1.includes(s2) || s2.includes(s1)) return 0.8;

    // Levenshtein distance calculation
    const matrix = Array(s2.length + 1).fill(null).map(() => Array(s1.length + 1).fill(null));

    for (let i = 0; i <= s1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= s2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= s2.length; j++) {
      for (let i = 1; i <= s1.length; i++) {
        const indicator = s1[i - 1] === s2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    const distance = matrix[s2.length][s1.length];
    const maxLength = Math.max(s1.length, s2.length);
    return 1 - (distance / maxLength);
  }, []);

  // Store recent words for sliding window detection
  const recentWordsRef = useRef<string[]>([]);
  // Store full transcript log for semantic matching
  const transcriptLogRef = useRef<string>('');

  // Process transcript for key point detection with sliding window
  const processTranscript = useCallback((transcript: string) => {
    if (!transcript.trim()) return;

    const cleanTranscript = transcript.toLowerCase().trim();

    // Notify parent of new transcript
    onTranscript?.(cleanTranscript);

    // Initial check for voice commands
    onVoiceCommand?.(cleanTranscript);

    // Add words to sliding window
    const words = cleanTranscript.split(/\s+/).filter(word => word.length > 2);
    // Add only new words since last update (handling interim vs final duplication is tricky, but this is simple sliding window)
    // Actually, simple appending might duplicate if we call this on interim frequently. 
    // We should rely on caller logic or just append everything for robustness in matching.
    recentWordsRef.current.push(...words);

    // Keep only last 20 words for context
    if (recentWordsRef.current.length > 20) {
      recentWordsRef.current = recentWordsRef.current.slice(-20);
    }

    // Also append to full log
    transcriptLogRef.current += " " + cleanTranscript;
    if (transcriptLogRef.current.length > 2000) {
      transcriptLogRef.current = transcriptLogRef.current.slice(-2000);
    }

    // Check for key points using sliding window AND full log for context if needed
    // We check against the recent window for quick matches
    keyPoints.forEach(keyPoint => {
      const keyPointWords = keyPoint.toLowerCase().split(/\s+/);
      const recentString = recentWordsRef.current.join(' ');

      // 1. Direct phrase match (fuzzy)
      if (fuzzyMatch(recentString, keyPoint.toLowerCase()) > 0.6) {
        console.log(`Key point fuzzy matched: "${keyPoint}"`);
        onKeyPointDetected?.(keyPoint, 0.9);
        return;
      }

      // 2. Word-by-word coverage check
      let matchCount = 0;
      keyPointWords.forEach(kpWord => {
        // Check if this word exists in recent history with fuzzy match
        const found = recentWordsRef.current.some(recentWord =>
          recentWord.includes(kpWord) || kpWord.includes(recentWord) || fuzzyMatch(kpWord, recentWord) > 0.75
        );
        if (found) matchCount++;
      });

      const confidence = matchCount / keyPointWords.length;

      if (confidence >= effectiveSensitivity) {
        console.log(`Key point words matched: "${keyPoint}" with confidence ${confidence}`);
        onKeyPointDetected?.(keyPoint, confidence);
      }
    });
  }, [keyPoints, effectiveSensitivity, fuzzyMatch, onVoiceCommand, onKeyPointDetected, onTranscript]);

  // Rest of component...

  // Initialize audio analysis for visual feedback
  const initializeAudio = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();
      const microphone = audioContextRef.current.createMediaStreamSource(stream);

      microphone.connect(analyserRef.current);
      analyserRef.current.fftSize = 256;

      const updateAudioLevel = () => {
        if (analyserRef.current && isListening) {
          const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
          analyserRef.current.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
          setAudioLevel(average / 255);
          animationRef.current = requestAnimationFrame(updateAudioLevel);
        }
      };

      updateAudioLevel();
    } catch (error) {
      console.error('Failed to initialize audio:', error);
      setError('Microphone access denied');
    }
  }, [isListening]);

  // Initialize speech recognition
  const initializeSpeechRecognition = useCallback(() => {
    if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      setError('Speech recognition not supported in this browser');
      return;
    }

    const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition;
    const recognition = new SpeechRecognition();

    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';
    recognition.maxAlternatives = 5;

    recognition.onstart = () => {
      setIsListening(true);
      setError(null);
    };

    recognition.onresult = (event) => {
      let interimTranscript = '';
      let finalTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      setCurrentTranscript(interimTranscript || finalTranscript);

      if (finalTranscript) {
        processTranscript(finalTranscript);
      }
    };

    recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      if (event.error !== 'no-speech' && event.error !== 'aborted') {
        setError(`Speech recognition error: ${event.error}`);
      }
    };

    recognition.onend = () => {
      if (isActive) {
        // Restart recognition automatically
        setTimeout(() => {
          try {
            recognition.start();
          } catch (err) {
            console.log('Failed to restart recognition:', err);
          }
        }, 100);
      } else {
        setIsListening(false);
      }
    };

    recognitionRef.current = recognition;
    return recognition;
  }, [isActive, processTranscript]);

  // Start/stop voice monitoring
  useEffect(() => {
    if (isActive) {
      const recognition = initializeSpeechRecognition();
      if (recognition) {
        try {
          recognition.start();
          initializeAudio();
        } catch (err) {
          console.error('Failed to start voice monitoring:', err);
          setError('Failed to start voice monitoring');
        }
      }
    } else {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      setIsListening(false);
      setCurrentTranscript('');
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [isActive, initializeSpeechRecognition, initializeAudio]);

  return (
    <div className="bg-white rounded-lg shadow-lg p-4 border border-gray-200">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          {isListening ? (
            <Mic className="text-green-600" size={20} />
          ) : (
            <MicOff className="text-gray-400" size={20} />
          )}
          <span className="font-medium text-gray-800">
            Enhanced Voice Monitor
          </span>
        </div>

        <div className="flex items-center gap-2">
          <Volume2 size={16} className="text-gray-500" />
          <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-green-500 transition-all duration-100"
              style={{ width: `${audioLevel * 100}%` }}
            />
          </div>
        </div>
      </div>

      {error && (
        <div className="flex items-center gap-2 text-red-600 text-sm mb-2">
          <AlertCircle size={16} />
          {error}
        </div>
      )}

      <div className="text-sm text-gray-600">
        <div className="flex justify-between items-center mb-1">
          <span>Status: {isListening ? 'Listening...' : 'Inactive'}</span>
          <button
            onClick={() => {
              if (recognitionRef.current) recognitionRef.current.stop();
              setIsListening(false);
              setTimeout(() => {
                initializeSpeechRecognition(); // Will auto-start if active
              }, 500);
            }}
            className="text-xs bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded text-gray-700"
          >
            Force Restart
          </button>
        </div>
        {currentTranscript && (
          <div className="text-blue-600 italic">
            "{currentTranscript}"
          </div>
        )}
      </div>

      <div className="mt-3 text-xs text-gray-500">
        Sensitivity: {Math.round(effectiveSensitivity * 100)}% |
        Key Points: {keyPoints.length}
      </div>
    </div>
  );
};

export default EnhancedVoiceMonitor;
