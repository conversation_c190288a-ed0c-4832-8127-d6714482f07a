import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface Settings {
  // Gesture Recognition Settings
  gestureEnabled: boolean;
  gestureSensitivity: number;
  gestureDetectionThreshold: number;
  gestureHoldDuration: number; // Duration in milliseconds to hold gesture for slide change
  mirrorCamera: boolean;
  showKeypoints: boolean;
  
  // Voice Monitoring Settings
  voiceEnabled: boolean;
  voiceSensitivity: number;
  keywordDetectionThreshold: number;
  voiceTimeout: number;
  
  // AI Processing Settings
  aiEnabled: boolean;
  aiResponseTime: string;
  ollamaModel: string;
  maxTokens: number;
  
  // Presentation Settings
  autoAdvance: boolean;
  autoAdvanceDelay: number;
  fullscreenOnStart: boolean;
  showSlideNumbers: boolean;
  enableSpotlight: boolean;
  
  // System Settings
  theme: string;
  language: string;
  enableNotifications: boolean;
  enableAnalytics: boolean;
}

interface SettingsContextType {
  settings: Settings;
  updateSetting: (key: keyof Settings, value: any) => void;
  saveSettings: () => Promise<void>;
  resetSettings: () => void;
  isLoading: boolean;
}

const defaultSettings: Settings = {
  // Gesture Recognition Settings
  gestureEnabled: true,
  gestureSensitivity: 0.7,
  gestureDetectionThreshold: 0.6,
  gestureHoldDuration: 3000, // 3 seconds default
  mirrorCamera: true,
  showKeypoints: true,
  
  // Voice Monitoring Settings
  voiceEnabled: true,
  voiceSensitivity: 0.8,
  keywordDetectionThreshold: 0.4, // Lower threshold for better detection
  voiceTimeout: 5000,
  
  // AI Processing Settings
  aiEnabled: true,
  aiResponseTime: 'balanced',
  ollamaModel: 'phi3',
  maxTokens: 500,
  
  // Presentation Settings
  autoAdvance: false,
  autoAdvanceDelay: 5000,
  fullscreenOnStart: false,
  showSlideNumbers: true,
  enableSpotlight: true,
  
  // System Settings
  theme: 'light',
  language: 'en',
  enableNotifications: true,
  enableAnalytics: true
};

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

interface SettingsProviderProps {
  children: ReactNode;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<Settings>(defaultSettings);
  const [isLoading, setIsLoading] = useState(true);

  // Load settings from localStorage on mount
  useEffect(() => {
    const loadSettings = () => {
      try {
        const savedSettings = localStorage.getItem('gestureProSettings');
        if (savedSettings) {
          const parsed = JSON.parse(savedSettings);
          setSettings({ ...defaultSettings, ...parsed });
        }
      } catch (error) {
        console.error('Failed to load settings:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);

  const updateSetting = (key: keyof Settings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSettings = async () => {
    try {
      localStorage.setItem('gestureProSettings', JSON.stringify(settings));
      console.log('Settings saved successfully');
    } catch (error) {
      console.error('Failed to save settings:', error);
      throw error;
    }
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
    localStorage.removeItem('gestureProSettings');
  };

  // Auto-save settings when they change
  useEffect(() => {
    if (!isLoading) {
      saveSettings();
    }
  }, [settings, isLoading]);

  const value: SettingsContextType = {
    settings,
    updateSetting,
    saveSettings,
    resetSettings,
    isLoading
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};
