/**
 * API utility functions with authentication support
 */

const API_BASE_URL = 'http://localhost:8000';

// Get auth token from localStorage
const getAuthToken = (): string | null => {
  return localStorage.getItem('auth_token');
};

// Create authenticated headers
const getAuthHeaders = (): HeadersInit => {
  const token = getAuthToken();
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return headers;
};

// Create authenticated headers for FormData
const getAuthHeadersForFormData = (): HeadersInit => {
  const token = getAuthToken();
  const headers: HeadersInit = {};

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return headers;
};

// Generic API request function
export const apiRequest = async (
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> => {
  const url = `${API_BASE_URL}${endpoint}`;

  // Merge auth headers with provided headers
  const headers = {
    ...getAuthHeaders(),
    ...options.headers,
  };

  const response = await fetch(url, {
    ...options,
    headers,
  });

  // Handle 401 Unauthorized
  if (response.status === 401) {
    // Clear auth data and redirect to login
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');
    window.location.href = '/auth';
    throw new Error('Authentication required');
  }

  return response;
};

// API functions for presentations
export const presentationAPI = {
  // Upload presentation
  upload: async (file: File, title?: string): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);
    if (title) {
      formData.append('title', title);
    }

    const response = await fetch(`${API_BASE_URL}/api/presentations/upload`, {
      method: 'POST',
      headers: getAuthHeadersForFormData(),
      body: formData,
    });

    if (response.status === 401) {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      window.location.href = '/auth';
      throw new Error('Authentication required');
    }

    return response.json();
  },

  // Get presentation status
  getStatus: async (presentationId: string): Promise<any> => {
    const response = await apiRequest(`/api/presentations/${presentationId}/status`);
    return response.json();
  },

  // Get presentation details
  get: async (presentationId: string): Promise<any> => {
    const response = await apiRequest(`/api/presentations/${presentationId}`);
    return response.json();
  },

  // List all presentations
  list: async (): Promise<any> => {
    const response = await apiRequest('/api/presentations');
    return response.json();
  },

  // Reanalyze presentation
  reanalyze: async (presentationId: string): Promise<any> => {
    const response = await apiRequest(`/api/presentations/${presentationId}/reanalyze`, {
      method: 'POST',
    });
    return response.json();
  },
};

// API functions for sessions
export const sessionAPI = {
  // Create session
  create: async (presentationId: string, mode: string = 'presenter'): Promise<any> => {
    const response = await apiRequest('/api/sessions/create', {
      method: 'POST',
      body: JSON.stringify({ presentation_id: presentationId, mode }),
    });
    return response.json();
  },

  // Get session
  get: async (sessionId: string): Promise<any> => {
    const response = await apiRequest(`/api/sessions/${sessionId}`);
    return response.json();
  },

  // Update session state
  update: async (sessionId: string, updates: { current_slide?: number, zoom_level?: number }): Promise<any> => {
    const response = await apiRequest(`/api/sessions/${sessionId}/update`, {
      method: 'POST',
      body: JSON.stringify(updates),
    });
    return response.json();
  },
};

// API functions for system health
export const systemAPI = {
  // Get health status
  health: async (): Promise<any> => {
    // Health endpoint doesn't require auth
    const response = await fetch(`${API_BASE_URL}/health`);
    return response.json();
  },

  // Get root info
  root: async (): Promise<any> => {
    // Root endpoint doesn't require auth
    const response = await fetch(`${API_BASE_URL}/`);
    return response.json();
  },
};

export default {
  apiRequest,
  presentationAPI,
  sessionAPI,
  systemAPI,
};
