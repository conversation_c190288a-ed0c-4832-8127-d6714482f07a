import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import PresentationViewer from '../components/PresentationViewer';
import LoadingSpinner from '../components/LoadingSpinner';
import { sessionAPI, presentationAPI } from '../lib/api';

interface Slide {
    id: string;
    content: string;
    imageUrl?: string;
    notes?: string;
    title?: string;
    keyPoints?: string[];
}

const AudiencePage: React.FC = () => {
    const { sessionId } = useParams<{ sessionId: string }>();
    const [slides, setSlides] = useState<Slide[]>([]);
    const [currentSlide, setCurrentSlide] = useState(0);
    const [presentationTitle, setPresentationTitle] = useState('');
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Initial load
    useEffect(() => {
        const loadData = async () => {
            try {
                if (!sessionId) {
                    setError('No session ID provided');
                    return;
                }

                const sessionRes = await sessionAPI.get(sessionId);
                if (!sessionRes.success) throw new Error('Failed to load session');

                const presentationId = sessionRes.data.session.presentation_id;

                // Fetch presentation details
                // Note: This assumes the audience user has permission or is the same user
                const presentationRes = await presentationAPI.getStatus(presentationId); // Using getStatus to get analysis result
                if (!presentationRes.success) throw new Error('Failed to load presentation');

                const analysis = presentationRes.data.analysis;
                setPresentationTitle(analysis?.title || 'Presentation');

                if (analysis?.slides) {
                    const convertedSlides: Slide[] = analysis.slides.map((slide: any, index: number) => ({
                        id: `slide-${index}`,
                        title: slide.title || `Slide ${index + 1}`,
                        content: slide.content || slide.text || '',
                        imageUrl: slide.image_url,
                        keyPoints: slide.key_points || [],
                        notes: slide.notes || ''
                    }));
                    setSlides(convertedSlides);
                } else {
                    // Fallback
                    const placeholderSlides: Slide[] = Array.from({ length: 5 }, (_, index) => ({
                        id: `slide-${index}`,
                        title: `Slide ${index + 1}`,
                        content: `<h2>Slide ${index + 1}</h2><p>Waiting for content...</p>`,
                    }));
                    setSlides(placeholderSlides);
                }

                setIsLoading(false);

            } catch (err: any) {
                console.error(err);
                setError(err.message || 'Error loading presentation');
                setIsLoading(false);
            }
        };

        loadData();
    }, [sessionId]);

    // Polling for updates
    useEffect(() => {
        if (!sessionId || isLoading || error) return;

        const interval = setInterval(async () => {
            try {
                const res = await sessionAPI.get(sessionId);
                // Log strictly for debug
                // console.log("Audience poll:", res);
                if (res.success && res.data.current_state) {
                    const newSlide = res.data.current_state.current_slide;
                    console.log(`Audience Poll: Server=${newSlide}, Local=${currentSlide}`);

                    if (newSlide !== undefined && newSlide !== currentSlide) {
                        console.log("Updating Audience Slide to:", newSlide);
                        setCurrentSlide(newSlide);
                    }
                }
            } catch (e) {
                console.error("Polling error", e);
            }
        }, 1000);

        return () => clearInterval(interval);
    }, [sessionId, isLoading, error, currentSlide]);

    if (isLoading) return <LoadingSpinner size="large" message="Joining session..." />;
    if (error) return <div className="p-8 text-center text-red-600">{error}</div>;

    return (
        <div className="h-screen w-screen bg-black overflow-hidden">
            <PresentationViewer
                presentationId="audience-view" // not used in logic much
                slides={slides}
                title={presentationTitle}
                currentSlide={currentSlide}
                // Disable controls for audience
                gestureControlEnabled={false}
                voiceMonitoringEnabled={false}
                onSlideChange={() => { }} // Read-only
            />
        </div>
    );
};

export default AudiencePage;
