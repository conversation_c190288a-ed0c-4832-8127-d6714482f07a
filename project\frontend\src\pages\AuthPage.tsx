import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import LoginForm from '../components/LoginForm';
import RegisterForm from '../components/RegisterForm';

const AuthPage: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  // Get the intended destination from location state
  const from = location.state?.from?.pathname || '/';

  const handleAuthSuccess = () => {
    // Redirect to intended destination or home
    navigate(from, { replace: true });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex flex-col">
      {/* Header */}
      <motion.header 
        className="p-6 bg-white/50 backdrop-blur-sm border-b border-gray-200"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="max-w-4xl mx-auto flex items-center gap-4">
          <button
            onClick={() => navigate('/')}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-6 h-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              {isLogin ? 'Sign In' : 'Create Account'}
            </h1>
            <p className="text-gray-600">
              {isLogin 
                ? 'Welcome back to AI-Powered Gesture Presentation System' 
                : 'Join the future of presentations'
              }
            </p>
          </div>
        </div>
      </motion.header>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="w-full max-w-md">
          <AnimatePresence mode="wait">
            {isLogin ? (
              <LoginForm
                key="login"
                onSwitchToRegister={() => setIsLogin(false)}
                onSuccess={handleAuthSuccess}
              />
            ) : (
              <RegisterForm
                key="register"
                onSwitchToLogin={() => setIsLogin(true)}
                onSuccess={handleAuthSuccess}
              />
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Features Section */}
      <motion.section 
        className="py-12 bg-white/50 backdrop-blur-sm"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              Why Choose AI GDPS?
            </h2>
            <p className="text-gray-600">
              Experience the next generation of presentation technology
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-6">
            <FeatureCard
              icon="🤖"
              title="AI-Powered Analysis"
              description="Intelligent content analysis and automatic key point extraction"
            />
            <FeatureCard
              icon="👋"
              title="Gesture Control"
              description="Navigate presentations naturally with hand gestures"
            />
            <FeatureCard
              icon="🎤"
              title="Voice Monitoring"
              description="Real-time feedback on your presentation delivery"
            />
          </div>
        </div>
      </motion.section>

      {/* Footer */}
      <footer className="py-6 text-center text-gray-600 bg-white/30">
        <p>Built with ❤️ by GECK Computer Science Team</p>
        <p className="text-sm mt-1">ZIYAD AHAMMED • DARSHAN S • SHARON • GEETHIKA R</p>
      </footer>
    </div>
  );
};

interface FeatureCardProps {
  icon: string;
  title: string;
  description: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description }) => (
  <motion.div
    className="bg-white/70 backdrop-blur-sm rounded-lg p-6 text-center border border-gray-200"
    whileHover={{ y: -5, scale: 1.02 }}
    transition={{ duration: 0.2 }}
  >
    <div className="text-3xl mb-3">{icon}</div>
    <h3 className="font-semibold text-gray-800 mb-2">{title}</h3>
    <p className="text-gray-600 text-sm">{description}</p>
  </motion.div>
);

export default AuthPage;
